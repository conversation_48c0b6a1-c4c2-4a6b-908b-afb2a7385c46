<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin role
require_role('admin');

$current_user = get_logged_in_user();
$user_id = $_GET['id'] ?? 0;

if (!$user_id) {
    header('Location: users.php');
    exit();
}

// Get user details
$stmt = $pdo->prepare("
    SELECT u.*, up.* 
    FROM users u 
    LEFT JOIN user_profiles up ON u.id = up.user_id 
    WHERE u.id = ?
");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: users.php');
    exit();
}

// Get user's stores if wholesaler
$stores = [];
if ($user['role'] === 'wholesaler') {
    $stmt = $pdo->prepare("SELECT * FROM stores WHERE wholesaler_id = ? ORDER BY created_at DESC");
    $stmt->execute([$user_id]);
    $stores = $stmt->fetchAll();
}

// Get user's orders if retailer
$orders = [];
if ($user['role'] === 'retailer') {
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE retailer_id = ? ORDER BY created_at DESC LIMIT 10");
    $stmt->execute([$user_id]);
    $orders = $stmt->fetchAll();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Details - Tradexa Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Admin</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="users.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-users mr-3"></i> Users
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="categories.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tags mr-3"></i> Categories
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="settings.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-cog mr-3"></i> Settings
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">User Details</h2>
                <a href="users.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Users
                </a>
            </div>

            <!-- User Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Name</label>
                                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Email</label>
                                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($user['email']); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Phone</label>
                                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($user['phone'] ?? 'Not provided'); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Role</label>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <?php echo ucfirst($user['role']); ?>
                                </span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Status</label>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    <?php echo $user['status'] === 'active' ? 'bg-green-100 text-green-800' : 
                                        ($user['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'); ?>">
                                    <?php echo ucfirst($user['status']); ?>
                                </span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Email Verified</label>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    <?php echo $user['email_verified'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                    <?php echo $user['email_verified'] ? 'Verified' : 'Not Verified'; ?>
                                </span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Registered</label>
                                <p class="text-sm text-gray-900"><?php echo date('M d, Y H:i', strtotime($user['created_at'])); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Last Login</label>
                                <p class="text-sm text-gray-900"><?php echo $user['last_login'] ? date('M d, Y H:i', strtotime($user['last_login'])) : 'Never'; ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Business Information -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Business Information</h3>
                    </div>
                    <div class="p-6">
                        <?php if ($user['business_name'] || $user['business_type'] || $user['gst_number']): ?>
                            <div class="space-y-4">
                                <?php if ($user['business_name']): ?>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Business Name</label>
                                        <p class="text-sm text-gray-900"><?php echo htmlspecialchars($user['business_name']); ?></p>
                                    </div>
                                <?php endif; ?>
                                <?php if ($user['business_type']): ?>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Business Type</label>
                                        <p class="text-sm text-gray-900"><?php echo htmlspecialchars($user['business_type']); ?></p>
                                    </div>
                                <?php endif; ?>
                                <?php if ($user['gst_number']): ?>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">GST Number</label>
                                        <p class="text-sm text-gray-900"><?php echo htmlspecialchars($user['gst_number']); ?></p>
                                    </div>
                                <?php endif; ?>
                                <?php if ($user['address_line1'] || $user['city']): ?>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Address</label>
                                        <p class="text-sm text-gray-900">
                                            <?php 
                                            $address_parts = array_filter([
                                                $user['address_line1'],
                                                $user['address_line2'],
                                                $user['city'],
                                                $user['state'],
                                                $user['pincode']
                                            ]);
                                            echo htmlspecialchars(implode(', ', $address_parts));
                                            ?>
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-gray-500">No business information provided</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Role-specific Information -->
            <?php if ($user['role'] === 'wholesaler' && !empty($stores)): ?>
                <div class="bg-white rounded-lg shadow mb-6">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Stores (<?php echo count($stores); ?>)</h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <?php foreach ($stores as $store): ?>
                                <div class="border rounded-lg p-4">
                                    <h4 class="font-medium text-gray-900"><?php echo htmlspecialchars($store['store_name']); ?></h4>
                                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($store['city']); ?></p>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                        <?php echo $store['status'] === 'active' ? 'bg-green-100 text-green-800' : 
                                            ($store['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'); ?>">
                                        <?php echo ucfirst($store['status']); ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($user['role'] === 'retailer' && !empty($orders)): ?>
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent Orders</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <?php foreach ($orders as $order): ?>
                                <div class="flex justify-between items-center border-b pb-2">
                                    <div>
                                        <p class="font-medium"><?php echo $order['order_number']; ?></p>
                                        <p class="text-sm text-gray-600"><?php echo date('M d, Y', strtotime($order['created_at'])); ?></p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-medium"><?php echo format_currency($order['final_amount']); ?></p>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <?php echo ucfirst($order['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
