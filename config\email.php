<?php
// Email configuration for Tradexa platform

// Email settings
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', ''); // You need to set this - use App Password for Gmail
define('SMTP_ENCRYPTION', 'tls');

define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Tradexa Platform');
define('REPLY_TO_EMAIL', '<EMAIL>');

// Simple SMTP email function (basic implementation)
function send_smtp_email($to, $subject, $message, $from_email = FROM_EMAIL, $from_name = FROM_NAME) {
    // For development/testing, we'll use a simple approach
    // In production, you should use PHPMailer or similar library
    
    // Set headers
    $headers = array();
    $headers[] = "MIME-Version: 1.0";
    $headers[] = "Content-type: text/html; charset=UTF-8";
    $headers[] = "From: {$from_name} <{$from_email}>";
    $headers[] = "Reply-To: {$from_email}";
    $headers[] = "X-Mailer: PHP/" . phpversion();
    
    // For development, we'll log emails instead of sending them
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
        return log_email($to, $subject, $message);
    }
    
    // Try to send email
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

// Log email for development (instead of actually sending)
function log_email($to, $subject, $message) {
    $log_dir = __DIR__ . '/../logs';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_file = $log_dir . '/emails.log';
    $timestamp = date('Y-m-d H:i:s');
    
    $log_content = "\n" . str_repeat('=', 50) . "\n";
    $log_content .= "EMAIL LOG - {$timestamp}\n";
    $log_content .= str_repeat('=', 50) . "\n";
    $log_content .= "To: {$to}\n";
    $log_content .= "Subject: {$subject}\n";
    $log_content .= "Message:\n{$message}\n";
    $log_content .= str_repeat('=', 50) . "\n";
    
    file_put_contents($log_file, $log_content, FILE_APPEND | LOCK_EX);
    
    return true; // Return true for development
}

// Email templates
function get_email_template($template_name, $variables = []) {
    $templates = [
        'otp_verification' => [
            'subject' => 'Tradexa - Email Verification OTP',
            'body' => '
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                        .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
                        .content { padding: 20px; background: #f9fafb; }
                        .otp { font-size: 24px; font-weight: bold; color: #2563eb; text-align: center; padding: 20px; background: white; border-radius: 8px; margin: 20px 0; }
                        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>Tradexa</h1>
                            <p>Wholesale E-Commerce Platform</p>
                        </div>
                        <div class="content">
                            <h2>Email Verification</h2>
                            <p>Hello {{name}},</p>
                            <p>Thank you for registering with Tradexa. To complete your registration, please use the following OTP:</p>
                            <div class="otp">{{otp}}</div>
                            <p><strong>This OTP will expire in 10 minutes.</strong></p>
                            <p>If you did not request this verification, please ignore this email.</p>
                        </div>
                        <div class="footer">
                            <p>&copy; 2024 Tradexa. All rights reserved.</p>
                            <p>This is an automated email. Please do not reply.</p>
                        </div>
                    </div>
                </body>
                </html>
            '
        ],
        'welcome' => [
            'subject' => 'Welcome to Tradexa!',
            'body' => '
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                        .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
                        .content { padding: 20px; background: #f9fafb; }
                        .button { display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0; }
                        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>Welcome to Tradexa!</h1>
                        </div>
                        <div class="content">
                            <h2>Hello {{name}},</h2>
                            <p>Welcome to Tradexa, the ultimate wholesale e-commerce platform!</p>
                            <p>Your account has been successfully verified and you can now:</p>
                            <ul>
                                <li>Browse thousands of wholesale products</li>
                                <li>Connect with verified suppliers</li>
                                <li>Request quotes and place bulk orders</li>
                                <li>Track your orders in real-time</li>
                            </ul>
                            <p>Get started by exploring our platform:</p>
                            <a href="{{login_url}}" class="button">Login to Your Account</a>
                        </div>
                        <div class="footer">
                            <p>&copy; 2024 Tradexa. All rights reserved.</p>
                        </div>
                    </div>
                </body>
                </html>
            '
        ]
    ];
    
    if (!isset($templates[$template_name])) {
        return false;
    }
    
    $template = $templates[$template_name];
    
    // Replace variables in template
    foreach ($variables as $key => $value) {
        $template['subject'] = str_replace('{{' . $key . '}}', $value, $template['subject']);
        $template['body'] = str_replace('{{' . $key . '}}', $value, $template['body']);
    }
    
    return $template;
}
?>
