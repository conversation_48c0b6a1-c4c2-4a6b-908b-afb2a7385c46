<?php
// Email configuration for Tradexa platform

// Email settings
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'vqgtsiqhjwaimbtn'); // Gmail App Password
define('SMTP_ENCRYPTION', 'tls');

define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Tradexa Platform');
define('REPLY_TO_EMAIL', '<EMAIL>');

// SMTP email function with proper TLS support
function send_smtp_email($to, $subject, $message, $from_email = FROM_EMAIL, $from_name = FROM_NAME) {
    // For development, we'll log emails instead of sending them
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
        return log_email($to, $subject, $message);
    }

    // Since <PERSON><PERSON>'s mail() function doesn't support STARTTLS properly,
    // we'll use a socket-based SMTP implementation
    return send_smtp_socket($to, $subject, $message, $from_email, $from_name);
}

// Socket-based SMTP implementation with STARTTLS support
function send_smtp_socket($to, $subject, $message, $from_email, $from_name) {
    $smtp_host = SMTP_HOST;
    $smtp_port = SMTP_PORT;
    $smtp_username = SMTP_USERNAME;
    $smtp_password = SMTP_PASSWORD;

    $debug_log = [];

    try {
        $debug_log[] = "Attempting to connect to $smtp_host:$smtp_port";

        // Create socket connection with SSL context
        $context = stream_context_create([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ]
        ]);

        $socket = stream_socket_client("tcp://$smtp_host:$smtp_port", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
        if (!$socket) {
            throw new Exception("Could not connect to SMTP server: $errstr ($errno)");
        }

        $debug_log[] = "Connected successfully";

        // Read initial response
        $response = fgets($socket, 515);
        $debug_log[] = "Initial response: " . trim($response);
        if (substr($response, 0, 3) != '220') {
            throw new Exception("SMTP Error: $response");
        }

        // Send EHLO
        $hostname = $_SERVER['SERVER_NAME'] ?? 'localhost';
        fputs($socket, "EHLO $hostname\r\n");
        $response = fgets($socket, 515);
        $debug_log[] = "EHLO response: " . trim($response);

        // Start TLS
        fputs($socket, "STARTTLS\r\n");
        $response = fgets($socket, 515);
        $debug_log[] = "STARTTLS response: " . trim($response);
        if (substr($response, 0, 3) != '220') {
            throw new Exception("STARTTLS failed: $response");
        }

        // Enable crypto
        if (!stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
            throw new Exception("Failed to enable TLS encryption");
        }
        $debug_log[] = "TLS encryption enabled";

        // Send EHLO again after TLS
        fputs($socket, "EHLO $hostname\r\n");
        $response = fgets($socket, 515);
        $debug_log[] = "EHLO after TLS response: " . trim($response);

        // Authenticate
        fputs($socket, "AUTH LOGIN\r\n");
        $response = fgets($socket, 515);
        $debug_log[] = "AUTH LOGIN response: " . trim($response);
        if (substr($response, 0, 3) != '334') {
            throw new Exception("AUTH LOGIN failed: $response");
        }

        fputs($socket, base64_encode($smtp_username) . "\r\n");
        $response = fgets($socket, 515);
        $debug_log[] = "Username auth response: " . trim($response);
        if (substr($response, 0, 3) != '334') {
            throw new Exception("Username authentication failed: $response");
        }

        fputs($socket, base64_encode($smtp_password) . "\r\n");
        $response = fgets($socket, 515);
        $debug_log[] = "Password auth response: " . trim($response);
        if (substr($response, 0, 3) != '235') {
            throw new Exception("Password authentication failed: $response");
        }

        $debug_log[] = "Authentication successful";

        // Send email
        fputs($socket, "MAIL FROM: <$from_email>\r\n");
        $response = fgets($socket, 515);
        $debug_log[] = "MAIL FROM response: " . trim($response);
        if (substr($response, 0, 3) != '250') {
            throw new Exception("MAIL FROM failed: $response");
        }

        fputs($socket, "RCPT TO: <$to>\r\n");
        $response = fgets($socket, 515);
        $debug_log[] = "RCPT TO response: " . trim($response);
        if (substr($response, 0, 3) != '250') {
            throw new Exception("RCPT TO failed: $response");
        }

        fputs($socket, "DATA\r\n");
        $response = fgets($socket, 515);
        $debug_log[] = "DATA response: " . trim($response);
        if (substr($response, 0, 3) != '354') {
            throw new Exception("DATA command failed: $response");
        }

        // Send headers and message
        $headers = "From: $from_name <$from_email>\r\n";
        $headers .= "To: $to\r\n";
        $headers .= "Subject: $subject\r\n";
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
        $headers .= "X-Mailer: Tradexa Platform\r\n";
        $headers .= "Date: " . date('r') . "\r\n";
        $headers .= "\r\n";

        fputs($socket, $headers . $message . "\r\n.\r\n");
        $response = fgets($socket, 515);
        $debug_log[] = "Message send response: " . trim($response);
        if (substr($response, 0, 3) != '250') {
            throw new Exception("Message sending failed: $response");
        }

        // Quit
        fputs($socket, "QUIT\r\n");
        $response = fgets($socket, 515);
        $debug_log[] = "QUIT response: " . trim($response);
        fclose($socket);

        $debug_log[] = "Email sent successfully!";
        log_debug("SMTP Success", $debug_log);

        return true;

    } catch (Exception $e) {
        $debug_log[] = "Error: " . $e->getMessage();
        log_debug("SMTP Error", $debug_log);
        error_log("SMTP Error: " . $e->getMessage());

        // Log email as fallback
        log_email($to, $subject, $message);
        return false;
    }
}

// Debug logging function
function log_debug($title, $debug_log) {
    $log_dir = __DIR__ . '/../logs';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }

    $log_file = $log_dir . '/smtp_debug.log';
    $timestamp = date('Y-m-d H:i:s');

    $log_content = "\n" . str_repeat('=', 50) . "\n";
    $log_content .= "$title - $timestamp\n";
    $log_content .= str_repeat('=', 50) . "\n";
    foreach ($debug_log as $entry) {
        $log_content .= "$entry\n";
    }
    $log_content .= str_repeat('=', 50) . "\n";

    file_put_contents($log_file, $log_content, FILE_APPEND | LOCK_EX);
}

// Log email for development (instead of actually sending)
function log_email($to, $subject, $message) {
    $log_dir = __DIR__ . '/../logs';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_file = $log_dir . '/emails.log';
    $timestamp = date('Y-m-d H:i:s');
    
    $log_content = "\n" . str_repeat('=', 50) . "\n";
    $log_content .= "EMAIL LOG - {$timestamp}\n";
    $log_content .= str_repeat('=', 50) . "\n";
    $log_content .= "To: {$to}\n";
    $log_content .= "Subject: {$subject}\n";
    $log_content .= "Message:\n{$message}\n";
    $log_content .= str_repeat('=', 50) . "\n";
    
    file_put_contents($log_file, $log_content, FILE_APPEND | LOCK_EX);
    
    return true; // Return true for development
}

// Email templates
function get_email_template($template_name, $variables = []) {
    $templates = [
        'otp_verification' => [
            'subject' => 'Tradexa - Email Verification OTP',
            'body' => '
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                        .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
                        .content { padding: 20px; background: #f9fafb; }
                        .otp { font-size: 24px; font-weight: bold; color: #2563eb; text-align: center; padding: 20px; background: white; border-radius: 8px; margin: 20px 0; }
                        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>Tradexa</h1>
                            <p>Wholesale E-Commerce Platform</p>
                        </div>
                        <div class="content">
                            <h2>Email Verification</h2>
                            <p>Hello {{name}},</p>
                            <p>Thank you for registering with Tradexa. To complete your registration, please use the following OTP:</p>
                            <div class="otp">{{otp}}</div>
                            <p><strong>This OTP will expire in 10 minutes.</strong></p>
                            <p>If you did not request this verification, please ignore this email.</p>
                        </div>
                        <div class="footer">
                            <p>&copy; 2024 Tradexa. All rights reserved.</p>
                            <p>This is an automated email. Please do not reply.</p>
                        </div>
                    </div>
                </body>
                </html>
            '
        ],
        'welcome' => [
            'subject' => 'Welcome to Tradexa!',
            'body' => '
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                        .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
                        .content { padding: 20px; background: #f9fafb; }
                        .button { display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0; }
                        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>Welcome to Tradexa!</h1>
                        </div>
                        <div class="content">
                            <h2>Hello {{name}},</h2>
                            <p>Welcome to Tradexa, the ultimate wholesale e-commerce platform!</p>
                            <p>Your account has been successfully verified and you can now:</p>
                            <ul>
                                <li>Browse thousands of wholesale products</li>
                                <li>Connect with verified suppliers</li>
                                <li>Request quotes and place bulk orders</li>
                                <li>Track your orders in real-time</li>
                            </ul>
                            <p>Get started by exploring our platform:</p>
                            <a href="{{login_url}}" class="button">Login to Your Account</a>
                        </div>
                        <div class="footer">
                            <p>&copy; 2024 Tradexa. All rights reserved.</p>
                        </div>
                    </div>
                </body>
                </html>
            '
        ]
    ];
    
    if (!isset($templates[$template_name])) {
        return false;
    }
    
    $template = $templates[$template_name];
    
    // Replace variables in template
    foreach ($variables as $key => $value) {
        $template['subject'] = str_replace('{{' . $key . '}}', $value, $template['subject']);
        $template['body'] = str_replace('{{' . $key . '}}', $value, $template['body']);
    }
    
    return $template;
}
?>
