<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require wholesaler role
require_role('wholesaler');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

// Get analytics data
$analytics = [];

// Total sales
$stmt = $pdo->prepare("
    SELECT SUM(oi.total_price) as total_sales, COUNT(DISTINCT o.id) as total_orders
    FROM order_items oi
    JOIN orders o ON oi.order_id = o.id
    JOIN stores s ON oi.store_id = s.id
    WHERE s.wholesaler_id = ?
");
$stmt->execute([$user_id]);
$sales_data = $stmt->fetch();
$analytics['total_sales'] = $sales_data['total_sales'] ?? 0;
$analytics['total_orders'] = $sales_data['total_orders'] ?? 0;

// Monthly sales
$stmt = $pdo->prepare("
    SELECT DATE_FORMAT(o.created_at, '%Y-%m') as month, 
           SUM(oi.total_price) as monthly_sales,
           COUNT(DISTINCT o.id) as monthly_orders
    FROM order_items oi
    JOIN orders o ON oi.order_id = o.id
    JOIN stores s ON oi.store_id = s.id
    WHERE s.wholesaler_id = ? AND o.created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(o.created_at, '%Y-%m')
    ORDER BY month DESC
");
$stmt->execute([$user_id]);
$monthly_data = $stmt->fetchAll();

// Top products
$stmt = $pdo->prepare("
    SELECT p.name, p.sku, SUM(oi.quantity) as total_sold, SUM(oi.total_price) as revenue
    FROM order_items oi
    JOIN products p ON oi.product_id = p.id
    JOIN stores s ON oi.store_id = s.id
    WHERE s.wholesaler_id = ?
    GROUP BY p.id
    ORDER BY total_sold DESC
    LIMIT 10
");
$stmt->execute([$user_id]);
$top_products = $stmt->fetchAll();

// Store performance
$stmt = $pdo->prepare("
    SELECT s.store_name, COUNT(DISTINCT o.id) as orders, SUM(oi.total_price) as revenue
    FROM stores s
    LEFT JOIN order_items oi ON s.id = oi.store_id
    LEFT JOIN orders o ON oi.order_id = o.id
    WHERE s.wholesaler_id = ?
    GROUP BY s.id
    ORDER BY revenue DESC
");
$stmt->execute([$user_id]);
$store_performance = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics - Tradexa Wholesaler</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Wholesaler</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> My Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Analytics</h2>
                <div class="flex space-x-2">
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>Last 30 Days</option>
                        <option>Last 3 Months</option>
                        <option>Last 6 Months</option>
                        <option>Last Year</option>
                    </select>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-dollar-sign text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Sales</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo format_currency($analytics['total_sales']); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-shopping-cart text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Orders</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($analytics['total_orders']); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-chart-line text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Avg Order Value</p>
                            <p class="text-2xl font-bold text-gray-900">
                                <?php echo $analytics['total_orders'] > 0 ? format_currency($analytics['total_sales'] / $analytics['total_orders']) : format_currency(0); ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-store text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Active Stores</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo count($store_performance); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts and Tables -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Monthly Sales Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Monthly Sales Trend</h3>
                    <canvas id="salesChart" width="400" height="200"></canvas>
                </div>

                <!-- Top Products -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Top Selling Products</h3>
                    <div class="space-y-3">
                        <?php if (empty($top_products)): ?>
                            <p class="text-gray-500">No sales data available</p>
                        <?php else: ?>
                            <?php foreach (array_slice($top_products, 0, 5) as $product): ?>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <p class="font-medium text-gray-900"><?php echo htmlspecialchars($product['name']); ?></p>
                                        <p class="text-sm text-gray-600">SKU: <?php echo htmlspecialchars($product['sku']); ?></p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-medium text-gray-900"><?php echo $product['total_sold']; ?> sold</p>
                                        <p class="text-sm text-gray-600"><?php echo format_currency($product['revenue']); ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Store Performance -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Store Performance</h3>
                </div>
                <div class="p-6">
                    <?php if (empty($store_performance)): ?>
                        <p class="text-gray-500">No store data available</p>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Store Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Order Value</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($store_performance as $store): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($store['store_name']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo number_format($store['orders'] ?? 0); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo format_currency($store['revenue'] ?? 0); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo ($store['orders'] ?? 0) > 0 ? format_currency(($store['revenue'] ?? 0) / $store['orders']) : format_currency(0); ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Monthly Sales Chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [<?php echo "'" . implode("','", array_reverse(array_column($monthly_data, 'month'))) . "'"; ?>],
                datasets: [{
                    label: 'Monthly Sales',
                    data: [<?php echo implode(',', array_reverse(array_column($monthly_data, 'monthly_sales'))); ?>],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
