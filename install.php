<?php
// Tradexa Installation Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if already installed
if (file_exists('config/installed.lock')) {
    die('Tradexa is already installed. Delete config/installed.lock to reinstall.');
}

$error = '';
$success = '';

if ($_POST) {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';
    $db_name = $_POST['db_name'] ?? 'tradexa_db';
    
    $admin_email = $_POST['admin_email'] ?? '';
    $admin_password = $_POST['admin_password'] ?? '';
    $admin_first_name = $_POST['admin_first_name'] ?? '';
    $admin_last_name = $_POST['admin_last_name'] ?? '';
    
    try {
        // Test database connection
        $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$db_name`");
        
        // Read and execute schema
        $schema = file_get_contents('database/schema.sql');
        $pdo->exec($schema);
        
        // Create admin user
        if ($admin_email && $admin_password) {
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET email = ?, password = ?, first_name = ?, last_name = ? WHERE role = 'admin'");
            $stmt->execute([$admin_email, $hashed_password, $admin_first_name, $admin_last_name]);
        }
        
        // Create config file
        $config_content = "<?php
// Database configuration
define('DB_HOST', '$db_host');
define('DB_USER', '$db_user');
define('DB_PASS', '$db_pass');
define('DB_NAME', '$db_name');

// Create connection
try {
    \$pdo = new PDO(\"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=utf8mb4\", DB_USER, DB_PASS);
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException \$e) {
    die(\"Connection failed: \" . \$e->getMessage());
}
?>";
        
        file_put_contents('config/database.php', $config_content);
        
        // Create directories
        $directories = [
            'uploads',
            'uploads/products',
            'uploads/stores',
            'uploads/users',
            'uploads/documents',
            'uploads/banners',
            'logs'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        // Create installation lock file
        file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
        
        $success = 'Installation completed successfully! You can now <a href="index.php">access your Tradexa platform</a>.';
        
    } catch (Exception $e) {
        $error = 'Installation failed: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tradexa Installation</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
            <h1 class="text-2xl font-bold text-center mb-6">Tradexa Installation</h1>
            
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo $success; ?>
                </div>
            <?php else: ?>
                <form method="POST">
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold mb-2">Database Configuration</h3>
                        
                        <label class="block text-sm font-medium mb-1">Database Host</label>
                        <input type="text" name="db_host" value="localhost" class="w-full p-2 border rounded" required>
                        
                        <label class="block text-sm font-medium mb-1 mt-2">Database User</label>
                        <input type="text" name="db_user" value="root" class="w-full p-2 border rounded" required>
                        
                        <label class="block text-sm font-medium mb-1 mt-2">Database Password</label>
                        <input type="password" name="db_pass" class="w-full p-2 border rounded">
                        
                        <label class="block text-sm font-medium mb-1 mt-2">Database Name</label>
                        <input type="text" name="db_name" value="tradexa_db" class="w-full p-2 border rounded" required>
                    </div>
                    
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold mb-2">Admin Account</h3>
                        
                        <label class="block text-sm font-medium mb-1">Email</label>
                        <input type="email" name="admin_email" class="w-full p-2 border rounded" required>
                        
                        <label class="block text-sm font-medium mb-1 mt-2">Password</label>
                        <input type="password" name="admin_password" class="w-full p-2 border rounded" required>
                        
                        <label class="block text-sm font-medium mb-1 mt-2">First Name</label>
                        <input type="text" name="admin_first_name" class="w-full p-2 border rounded" required>
                        
                        <label class="block text-sm font-medium mb-1 mt-2">Last Name</label>
                        <input type="text" name="admin_last_name" class="w-full p-2 border rounded" required>
                    </div>
                    
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">
                        Install Tradexa
                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
