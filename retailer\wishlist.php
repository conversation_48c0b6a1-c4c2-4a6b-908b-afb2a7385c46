<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require retailer role
require_role('retailer');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

// Get wishlist items
$stmt = $pdo->prepare("
    SELECT w.*, p.name, p.sku, p.base_price, p.min_order_quantity, p.unit, p.stock_quantity,
           s.store_name, s.city, c.name as category_name,
           (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
    FROM wishlists w
    JOIN products p ON w.product_id = p.id
    JOIN stores s ON p.store_id = s.id
    LEFT JOIN categories c ON p.category_id = c.id
    WHERE w.user_id = ? AND p.status = 'active'
    ORDER BY w.added_at DESC
");
$stmt->execute([$user_id]);
$wishlist_items = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wishlist - Tradexa</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="browse.php" class="text-gray-700 hover:text-blue-600">Browse Products</a>
                    <a href="cart.php" class="text-gray-700 hover:text-blue-600">
                        <i class="fas fa-shopping-cart"></i> Cart
                    </a>
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="browse.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-search mr-3"></i> Browse Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> My Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="wishlist.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-heart mr-3"></i> Wishlist
                        </a>
                    </li>
                    <li>
                        <a href="suppliers.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Followed Suppliers
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">My Wishlist</h2>
                <?php if (!empty($wishlist_items)): ?>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        <i class="fas fa-cart-plus mr-1"></i> Add All to Cart
                    </button>
                <?php endif; ?>
            </div>

            <?php if (empty($wishlist_items)): ?>
                <!-- Empty Wishlist -->
                <div class="bg-white rounded-lg shadow p-8 text-center">
                    <i class="fas fa-heart text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Your Wishlist is Empty</h3>
                    <p class="text-gray-600 mb-4">Save products you love to your wishlist and come back to them later.</p>
                    <a href="browse.php" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                        Browse Products
                    </a>
                </div>
            <?php else: ?>
                <!-- Wishlist Items -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            Saved Items (<?php echo count($wishlist_items); ?>)
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            <?php foreach ($wishlist_items as $item): ?>
                                <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow product-card">
                                    <?php if ($item['primary_image']): ?>
                                        <img src="../<?php echo $item['primary_image']; ?>" alt="<?php echo htmlspecialchars($item['name']); ?>" class="w-full h-32 object-cover rounded mb-3">
                                    <?php else: ?>
                                        <div class="w-full h-32 bg-gray-200 rounded mb-3 flex items-center justify-center">
                                            <i class="fas fa-image text-gray-400 text-2xl"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <h4 class="font-semibold text-gray-900 mb-1"><?php echo htmlspecialchars($item['name']); ?></h4>
                                    <p class="text-sm text-gray-600 mb-1"><?php echo htmlspecialchars($item['store_name']); ?>, <?php echo htmlspecialchars($item['city']); ?></p>
                                    <p class="text-sm text-gray-600 mb-2"><?php echo htmlspecialchars($item['category_name'] ?? 'Uncategorized'); ?></p>
                                    
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-lg font-bold text-green-600"><?php echo format_currency($item['base_price']); ?></span>
                                        <button class="text-red-500 hover:text-red-700 remove-from-wishlist" data-product-id="<?php echo $item['product_id']; ?>">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                    
                                    <div class="text-xs text-gray-500 mb-3">
                                        <p>Min Order: <?php echo $item['min_order_quantity']; ?> <?php echo $item['unit']; ?></p>
                                        <p>Stock: <?php echo $item['stock_quantity']; ?> <?php echo $item['unit']; ?></p>
                                        <p>Added: <?php echo date('M d, Y', strtotime($item['added_at'])); ?></p>
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <button class="flex-1 bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 add-to-cart-btn" 
                                                data-product-id="<?php echo $item['product_id']; ?>" data-quantity="<?php echo $item['min_order_quantity']; ?>">
                                            <i class="fas fa-cart-plus mr-1"></i> Add to Cart
                                        </button>
                                        <button class="flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700">
                                            <i class="fas fa-quote-left mr-1"></i> RFQ
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="../assets/js/app.js"></script>
    <script>
        // Remove from wishlist functionality
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-from-wishlist') || e.target.parentElement.classList.contains('remove-from-wishlist')) {
                e.preventDefault();
                const button = e.target.classList.contains('remove-from-wishlist') ? e.target : e.target.parentElement;
                const productId = button.dataset.productId;
                
                if (confirm('Remove this item from your wishlist?')) {
                    // AJAX call to remove from wishlist
                    fetch('/api/wishlist.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'remove',
                            product_id: productId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Remove item from DOM
                            button.closest('.product-card').remove();
                            // Show notification
                            if (window.Tradexa && window.Tradexa.Utils) {
                                window.Tradexa.Utils.showNotification('Item removed from wishlist', 'success');
                            }
                        } else {
                            alert('Failed to remove item from wishlist');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error removing item from wishlist');
                    });
                }
            }
        });
    </script>
</body>
</html>
