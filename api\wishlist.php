<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Require login
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit();
}

$user_id = $_SESSION['user_id'];
$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'add':
                $product_id = $input['product_id'] ?? 0;
                
                if (!$product_id) {
                    throw new Exception('Product ID is required');
                }
                
                // Check if product exists
                $stmt = $pdo->prepare("SELECT id FROM products WHERE id = ? AND status = 'active'");
                $stmt->execute([$product_id]);
                if (!$stmt->fetch()) {
                    throw new Exception('Product not found');
                }
                
                // Add to wishlist (ignore if already exists due to UNIQUE constraint)
                $stmt = $pdo->prepare("INSERT IGNORE INTO wishlists (user_id, product_id) VALUES (?, ?)");
                $stmt->execute([$user_id, $product_id]);
                
                echo json_encode(['success' => true, 'message' => 'Added to wishlist']);
                break;
                
            case 'remove':
                $product_id = $input['product_id'] ?? 0;
                
                if (!$product_id) {
                    throw new Exception('Product ID is required');
                }
                
                $stmt = $pdo->prepare("DELETE FROM wishlists WHERE user_id = ? AND product_id = ?");
                $stmt->execute([$user_id, $product_id]);
                
                echo json_encode(['success' => true, 'message' => 'Removed from wishlist']);
                break;
                
            case 'toggle':
                $product_id = $input['product_id'] ?? 0;
                
                if (!$product_id) {
                    throw new Exception('Product ID is required');
                }
                
                // Check if already in wishlist
                $stmt = $pdo->prepare("SELECT id FROM wishlists WHERE user_id = ? AND product_id = ?");
                $stmt->execute([$user_id, $product_id]);
                $exists = $stmt->fetch();
                
                if ($exists) {
                    // Remove from wishlist
                    $stmt = $pdo->prepare("DELETE FROM wishlists WHERE user_id = ? AND product_id = ?");
                    $stmt->execute([$user_id, $product_id]);
                    $message = 'Removed from wishlist';
                    $in_wishlist = false;
                } else {
                    // Add to wishlist
                    $stmt = $pdo->prepare("INSERT INTO wishlists (user_id, product_id) VALUES (?, ?)");
                    $stmt->execute([$user_id, $product_id]);
                    $message = 'Added to wishlist';
                    $in_wishlist = true;
                }
                
                echo json_encode([
                    'success' => true, 
                    'message' => $message,
                    'in_wishlist' => $in_wishlist
                ]);
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } elseif ($method === 'GET') {
        // Get wishlist items
        $stmt = $pdo->prepare("
            SELECT w.*, p.name, p.sku, p.base_price, p.unit, p.min_order_quantity,
                   s.store_name, s.city,
                   (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as image
            FROM wishlists w
            JOIN products p ON w.product_id = p.id
            JOIN stores s ON p.store_id = s.id
            WHERE w.user_id = ? AND p.status = 'active'
            ORDER BY w.added_at DESC
        ");
        $stmt->execute([$user_id]);
        $items = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'items' => $items,
            'count' => count($items)
        ]);
        
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
