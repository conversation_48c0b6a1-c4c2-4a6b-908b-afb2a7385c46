<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require wholesaler role
require_role('wholesaler');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

$error = '';
$success = '';

// Handle form submissions
if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'add_store') {
            $store_name = sanitize_input($_POST['store_name']);
            $description = sanitize_input($_POST['description']);
            $business_type = sanitize_input($_POST['business_type']);
            $address_line1 = sanitize_input($_POST['address_line1']);
            $address_line2 = sanitize_input($_POST['address_line2']);
            $city = sanitize_input($_POST['city']);
            $state = sanitize_input($_POST['state']);
            $pincode = sanitize_input($_POST['pincode']);
            $contact_phone = sanitize_input($_POST['contact_phone']);
            $contact_email = sanitize_input($_POST['contact_email']);
            
            if (empty($store_name) || empty($city) || empty($contact_phone)) {
                $error = 'Please fill in all required fields';
            } else {
                try {
                    $store_slug = generate_slug($store_name);
                    
                    // Check if slug already exists
                    $stmt = $pdo->prepare("SELECT id FROM stores WHERE store_slug = ?");
                    $stmt->execute([$store_slug]);
                    if ($stmt->fetch()) {
                        $store_slug .= '-' . uniqid();
                    }
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO stores (wholesaler_id, store_name, store_slug, description, business_type, 
                                          address_line1, address_line2, city, state, pincode, contact_phone, contact_email) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $user_id, $store_name, $store_slug, $description, $business_type,
                        $address_line1, $address_line2, $city, $state, $pincode, $contact_phone, $contact_email
                    ]);
                    
                    $success = 'Store created successfully! It will be reviewed by admin before activation.';
                    
                    // Track event
                    track_event('store_created', ['store_name' => $store_name], $user_id);
                    
                } catch (Exception $e) {
                    $error = 'Failed to create store. Please try again.';
                }
            }
        }
    }
}

// Get user's stores
$stmt = $pdo->prepare("SELECT * FROM stores WHERE wholesaler_id = ? ORDER BY created_at DESC");
$stmt->execute([$user_id]);
$stores = $stmt->fetchAll();

$show_add_form = isset($_GET['action']) && $_GET['action'] === 'add';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Stores - Tradexa Wholesaler</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Wholesaler</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-store mr-3"></i> My Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">My Stores</h2>
                <?php if (!$show_add_form): ?>
                    <a href="?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        <i class="fas fa-plus mr-1"></i> Add New Store
                    </a>
                <?php endif; ?>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <?php if ($show_add_form): ?>
                <!-- Add Store Form -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                    <h3 class="text-xl font-bold mb-4">Add New Store</h3>
                    <form method="POST" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="add_store">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Store Name *</label>
                                <input type="text" name="store_name" required class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Business Type</label>
                                <select name="business_type" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Select Business Type</option>
                                    <option value="manufacturer">Manufacturer</option>
                                    <option value="distributor">Distributor</option>
                                    <option value="wholesaler">Wholesaler</option>
                                    <option value="trader">Trader</option>
                                </select>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea name="description" rows="3" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"></textarea>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                                <input type="text" name="address_line1" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Address Line 2</label>
                                <input type="text" name="address_line2" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">City *</label>
                                <input type="text" name="city" required class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">State</label>
                                <input type="text" name="state" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Pincode</label>
                                <input type="text" name="pincode" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Contact Phone *</label>
                                <input type="tel" name="contact_phone" required class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                                <input type="email" name="contact_email" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        
                        <div class="flex space-x-4">
                            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-save mr-1"></i> Create Store
                            </button>
                            <a href="stores.php" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            <?php endif; ?>

            <!-- Stores List -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Your Stores</h3>
                </div>
                <div class="p-6">
                    <?php if (empty($stores)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-store text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500 mb-4">You haven't created any stores yet</p>
                            <a href="?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                Create Your First Store
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php foreach ($stores as $store): ?>
                                <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow">
                                    <div class="flex justify-between items-start mb-3">
                                        <h4 class="font-bold text-lg"><?php echo htmlspecialchars($store['store_name']); ?></h4>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            <?php echo $store['status'] === 'active' ? 'bg-green-100 text-green-800' : 
                                                ($store['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'); ?>">
                                            <?php echo ucfirst($store['status']); ?>
                                        </span>
                                    </div>
                                    
                                    <?php if ($store['description']): ?>
                                        <p class="text-gray-600 text-sm mb-2"><?php echo htmlspecialchars($store['description']); ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="text-sm text-gray-500 space-y-1">
                                        <p><i class="fas fa-map-marker-alt mr-1"></i> <?php echo htmlspecialchars($store['city']); ?></p>
                                        <p><i class="fas fa-phone mr-1"></i> <?php echo htmlspecialchars($store['contact_phone']); ?></p>
                                        <?php if ($store['business_type']): ?>
                                            <p><i class="fas fa-building mr-1"></i> <?php echo ucfirst($store['business_type']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mt-4 flex space-x-2">
                                        <a href="products.php?store_id=<?php echo $store['id']; ?>" class="text-blue-600 hover:text-blue-800 text-sm">
                                            <i class="fas fa-box mr-1"></i> Products
                                        </a>
                                        <a href="store-edit.php?id=<?php echo $store['id']; ?>" class="text-green-600 hover:text-green-800 text-sm">
                                            <i class="fas fa-edit mr-1"></i> Edit
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
