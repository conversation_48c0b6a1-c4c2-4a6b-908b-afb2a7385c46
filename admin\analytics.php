<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin role
require_role('admin');

$current_user = get_logged_in_user();

// Get platform statistics
$stats = [];

// Total users by role
$stmt = $pdo->query("SELECT role, COUNT(*) as count FROM users WHERE role != 'admin' GROUP BY role");
$user_stats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
$stats['total_wholesalers'] = $user_stats['wholesaler'] ?? 0;
$stats['total_retailers'] = $user_stats['retailer'] ?? 0;

// Total stores
$stmt = $pdo->query("SELECT COUNT(*) as total FROM stores");
$stats['total_stores'] = $stmt->fetch()['total'];

// Active stores
$stmt = $pdo->query("SELECT COUNT(*) as total FROM stores WHERE status = 'active'");
$stats['active_stores'] = $stmt->fetch()['total'];

// Total products
$stmt = $pdo->query("SELECT COUNT(*) as total FROM products");
$stats['total_products'] = $stmt->fetch()['total'];

// Total orders
$stmt = $pdo->query("SELECT COUNT(*) as total FROM orders");
$stats['total_orders'] = $stmt->fetch()['total'];

// Total revenue
$stmt = $pdo->query("SELECT SUM(final_amount) as total FROM orders WHERE status != 'cancelled'");
$stats['total_revenue'] = $stmt->fetch()['total'] ?? 0;

// Monthly registrations
$stmt = $pdo->query("
    SELECT DATE_FORMAT(created_at, '%Y-%m') as month, 
           COUNT(*) as registrations
    FROM users 
    WHERE role != 'admin' AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
    ORDER BY month DESC
");
$monthly_registrations = $stmt->fetchAll();

// Monthly orders
$stmt = $pdo->query("
    SELECT DATE_FORMAT(created_at, '%Y-%m') as month, 
           COUNT(*) as orders,
           SUM(final_amount) as revenue
    FROM orders 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
    ORDER BY month DESC
");
$monthly_orders = $stmt->fetchAll();

// Top categories
$stmt = $pdo->query("
    SELECT c.name, COUNT(p.id) as product_count
    FROM categories c
    LEFT JOIN products p ON c.id = p.category_id
    WHERE c.is_active = 1
    GROUP BY c.id
    ORDER BY product_count DESC
    LIMIT 10
");
$top_categories = $stmt->fetchAll();

// Recent activities
$stmt = $pdo->query("
    SELECT 'user_registration' as type, u.first_name, u.last_name, u.role, u.created_at as activity_date
    FROM users u 
    WHERE u.role != 'admin' AND u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    UNION ALL
    SELECT 'store_created' as type, u.first_name, u.last_name, s.store_name as role, s.created_at as activity_date
    FROM stores s
    JOIN users u ON s.wholesaler_id = u.id
    WHERE s.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    ORDER BY activity_date DESC
    LIMIT 10
");
$recent_activities = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics - Tradexa Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Admin</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="users.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-users mr-3"></i> Users
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="categories.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tags mr-3"></i> Categories
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="settings.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-cog mr-3"></i> Settings
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Platform Analytics</h2>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-users text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Users</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_wholesalers'] + $stats['total_retailers']); ?></p>
                            <p class="text-xs text-gray-500"><?php echo $stats['total_wholesalers']; ?> Wholesalers, <?php echo $stats['total_retailers']; ?> Retailers</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-store text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Stores</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_stores']); ?></p>
                            <p class="text-xs text-gray-500"><?php echo $stats['active_stores']; ?> Active</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-box text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Products</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_products']); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-shopping-cart text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Orders</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_orders']); ?></p>
                            <p class="text-xs text-gray-500"><?php echo format_currency($stats['total_revenue']); ?> Revenue</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Monthly Registrations -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Monthly User Registrations</h3>
                    <canvas id="registrationsChart" width="400" height="200"></canvas>
                </div>

                <!-- Monthly Orders -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Monthly Orders & Revenue</h3>
                    <canvas id="ordersChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Tables -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Top Categories -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Top Categories</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <?php foreach ($top_categories as $category): ?>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($category['name']); ?></span>
                                    <span class="text-sm text-gray-600"><?php echo number_format($category['product_count']); ?> products</span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent Activities</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-<?php echo $activity['type'] === 'user_registration' ? 'user-plus' : 'store'; ?> text-blue-600"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($activity['first_name'] . ' ' . $activity['last_name']); ?>
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            <?php 
                                            if ($activity['type'] === 'user_registration') {
                                                echo 'Registered as ' . ucfirst($activity['role']);
                                            } else {
                                                echo 'Created store: ' . htmlspecialchars($activity['role']);
                                            }
                                            ?>
                                        </p>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <?php echo time_ago($activity['activity_date']); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Monthly Registrations Chart
        const regCtx = document.getElementById('registrationsChart').getContext('2d');
        new Chart(regCtx, {
            type: 'line',
            data: {
                labels: [<?php echo "'" . implode("','", array_reverse(array_column($monthly_registrations, 'month'))) . "'"; ?>],
                datasets: [{
                    label: 'Registrations',
                    data: [<?php echo implode(',', array_reverse(array_column($monthly_registrations, 'registrations'))); ?>],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Monthly Orders Chart
        const ordCtx = document.getElementById('ordersChart').getContext('2d');
        new Chart(ordCtx, {
            type: 'bar',
            data: {
                labels: [<?php echo "'" . implode("','", array_reverse(array_column($monthly_orders, 'month'))) . "'"; ?>],
                datasets: [{
                    label: 'Orders',
                    data: [<?php echo implode(',', array_reverse(array_column($monthly_orders, 'orders'))); ?>],
                    backgroundColor: 'rgba(34, 197, 94, 0.8)',
                    yAxisID: 'y'
                }, {
                    label: 'Revenue',
                    data: [<?php echo implode(',', array_reverse(array_column($monthly_orders, 'revenue'))); ?>],
                    backgroundColor: 'rgba(168, 85, 247, 0.8)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: true,
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    </script>
</body>
</html>
