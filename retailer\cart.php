<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require retailer role
require_role('retailer');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

// Get cart items
$stmt = $pdo->prepare("
    SELECT ci.*, p.name, p.sku, p.base_price, p.unit, p.min_order_quantity, p.stock_quantity,
           s.store_name, s.city,
           (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
    FROM cart_items ci
    JOIN carts c ON ci.cart_id = c.id
    JOIN products p ON ci.product_id = p.id
    JOIN stores s ON p.store_id = s.id
    WHERE c.user_id = ? AND p.status = 'active'
    ORDER BY ci.created_at DESC
");
$stmt->execute([$user_id]);
$cart_items = $stmt->fetchAll();

// Calculate totals
$subtotal = 0;
$total_items = 0;
foreach ($cart_items as &$item) {
    $item['total_price'] = $item['quantity'] * $item['unit_price'];
    $subtotal += $item['total_price'];
    $total_items += $item['quantity'];
}

$shipping = 0; // Calculate shipping based on business logic
$tax = $subtotal * 0.18; // 18% GST
$total = $subtotal + $shipping + $tax;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - Tradexa</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="browse.php" class="text-gray-700 hover:text-blue-600">Browse Products</a>
                    <a href="cart.php" class="text-blue-600 font-medium">
                        <i class="fas fa-shopping-cart"></i> Cart (<?php echo $total_items; ?>)
                    </a>
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="browse.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-search mr-3"></i> Browse Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> My Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="wishlist.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-heart mr-3"></i> Wishlist
                        </a>
                    </li>
                    <li>
                        <a href="suppliers.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Followed Suppliers
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Shopping Cart</h2>
                <a href="browse.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    <i class="fas fa-plus mr-1"></i> Continue Shopping
                </a>
            </div>

            <?php if (empty($cart_items)): ?>
                <!-- Empty Cart -->
                <div class="bg-white rounded-lg shadow p-8 text-center">
                    <i class="fas fa-shopping-cart text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Your Cart is Empty</h3>
                    <p class="text-gray-600 mb-4">Add some products to your cart to get started.</p>
                    <a href="browse.php" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                        Browse Products
                    </a>
                </div>
            <?php else: ?>
                <!-- Cart Items -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Cart Items List -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-lg shadow">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900">Cart Items (<?php echo count($cart_items); ?>)</h3>
                            </div>
                            <div class="p-6">
                                <div class="space-y-6">
                                    <?php foreach ($cart_items as $item): ?>
                                        <div class="flex items-center space-x-4 cart-item" data-product-id="<?php echo $item['product_id']; ?>">
                                            <?php if ($item['primary_image']): ?>
                                                <img src="../<?php echo $item['primary_image']; ?>" alt="<?php echo htmlspecialchars($item['name']); ?>" class="w-16 h-16 object-cover rounded">
                                            <?php else: ?>
                                                <div class="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400"></i>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="flex-1">
                                                <h4 class="font-medium text-gray-900"><?php echo htmlspecialchars($item['name']); ?></h4>
                                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($item['store_name']); ?>, <?php echo htmlspecialchars($item['city']); ?></p>
                                                <p class="text-sm text-gray-600">SKU: <?php echo htmlspecialchars($item['sku']); ?></p>
                                                <p class="text-sm text-gray-600">Unit Price: <?php echo format_currency($item['unit_price']); ?></p>
                                            </div>
                                            
                                            <div class="flex items-center space-x-2">
                                                <button class="quantity-btn" data-action="decrease" data-product-id="<?php echo $item['product_id']; ?>">
                                                    <i class="fas fa-minus text-gray-600 hover:text-red-600"></i>
                                                </button>
                                                <input type="number" value="<?php echo $item['quantity']; ?>" min="<?php echo $item['min_order_quantity']; ?>" max="<?php echo $item['stock_quantity']; ?>" 
                                                       class="w-16 text-center border border-gray-300 rounded quantity-input" data-product-id="<?php echo $item['product_id']; ?>">
                                                <button class="quantity-btn" data-action="increase" data-product-id="<?php echo $item['product_id']; ?>">
                                                    <i class="fas fa-plus text-gray-600 hover:text-green-600"></i>
                                                </button>
                                            </div>
                                            
                                            <div class="text-right">
                                                <p class="font-bold text-gray-900"><?php echo format_currency($item['total_price']); ?></p>
                                                <button class="text-red-600 hover:text-red-800 text-sm remove-item" data-product-id="<?php echo $item['product_id']; ?>">
                                                    <i class="fas fa-trash mr-1"></i> Remove
                                                </button>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Order Summary</h3>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Subtotal (<?php echo $total_items; ?> items)</span>
                                    <span class="font-medium"><?php echo format_currency($subtotal); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Shipping</span>
                                    <span class="font-medium"><?php echo $shipping > 0 ? format_currency($shipping) : 'Free'; ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Tax (GST 18%)</span>
                                    <span class="font-medium"><?php echo format_currency($tax); ?></span>
                                </div>
                                <div class="border-t pt-3">
                                    <div class="flex justify-between">
                                        <span class="text-lg font-bold">Total</span>
                                        <span class="text-lg font-bold text-blue-600"><?php echo format_currency($total); ?></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-6 space-y-3">
                                <button class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 font-medium">
                                    <i class="fas fa-credit-card mr-2"></i> Proceed to Checkout
                                </button>
                                <button class="w-full bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700" id="clear-cart">
                                    <i class="fas fa-trash mr-2"></i> Clear Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="../assets/js/app.js"></script>
    <script>
        // Cart functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Quantity buttons
            document.querySelectorAll('.quantity-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const action = this.dataset.action;
                    const productId = this.dataset.productId;
                    const input = document.querySelector(`.quantity-input[data-product-id="${productId}"]`);
                    let quantity = parseInt(input.value);
                    
                    if (action === 'increase') {
                        quantity++;
                    } else if (action === 'decrease' && quantity > 1) {
                        quantity--;
                    }
                    
                    input.value = quantity;
                    updateCartItem(productId, quantity);
                });
            });
            
            // Quantity input change
            document.querySelectorAll('.quantity-input').forEach(input => {
                input.addEventListener('change', function() {
                    const productId = this.dataset.productId;
                    const quantity = parseInt(this.value);
                    updateCartItem(productId, quantity);
                });
            });
            
            // Remove item buttons
            document.querySelectorAll('.remove-item').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.dataset.productId;
                    if (confirm('Remove this item from cart?')) {
                        removeCartItem(productId);
                    }
                });
            });
            
            // Clear cart button
            document.getElementById('clear-cart')?.addEventListener('click', function() {
                if (confirm('Clear all items from cart?')) {
                    clearCart();
                }
            });
        });
        
        function updateCartItem(productId, quantity) {
            fetch('../api/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'update',
                    product_id: productId,
                    quantity: quantity
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload(); // Refresh to update totals
                } else {
                    alert('Failed to update cart: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating cart');
            });
        }
        
        function removeCartItem(productId) {
            fetch('../api/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'remove',
                    product_id: productId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to remove item: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error removing item');
            });
        }
        
        function clearCart() {
            fetch('../api/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'clear'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to clear cart: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error clearing cart');
            });
        }
    </script>
</body>
</html>
