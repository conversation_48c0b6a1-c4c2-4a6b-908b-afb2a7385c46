<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require retailer role
require_role('retailer');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

// Get orders for this retailer
$stmt = $pdo->prepare("
    SELECT o.*, 
           (SELECT COUNT(*) FROM order_items WHERE order_id = o.id) as item_count
    FROM orders o 
    WHERE o.retailer_id = ? 
    ORDER BY o.created_at DESC
");
$stmt->execute([$user_id]);
$orders = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Orders - Tradexa</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="browse.php" class="text-gray-700 hover:text-blue-600">Browse Products</a>
                    <a href="cart.php" class="text-gray-700 hover:text-blue-600">
                        <i class="fas fa-shopping-cart"></i> Cart
                    </a>
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="browse.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-search mr-3"></i> Browse Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> My Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="wishlist.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-heart mr-3"></i> Wishlist
                        </a>
                    </li>
                    <li>
                        <a href="suppliers.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Followed Suppliers
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">My Orders</h2>
            </div>

            <?php if (empty($orders)): ?>
                <!-- No Orders -->
                <div class="bg-white rounded-lg shadow p-8 text-center">
                    <i class="fas fa-shopping-cart text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Orders Yet</h3>
                    <p class="text-gray-600 mb-4">You haven't placed any orders yet. Start shopping to see your orders here.</p>
                    <a href="browse.php" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                        Browse Products
                    </a>
                </div>
            <?php else: ?>
                <!-- Orders List -->
                <div class="space-y-6">
                    <?php foreach ($orders as $order): ?>
                        <div class="bg-white rounded-lg shadow">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">
                                            Order #<?php echo $order['order_number']; ?>
                                        </h3>
                                        <p class="text-sm text-gray-600">
                                            Order Date: <?php echo date('M d, Y H:i', strtotime($order['created_at'])); ?>
                                        </p>
                                        <p class="text-sm text-gray-600">
                                            Items: <?php echo $order['item_count']; ?>
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                                            <?php 
                                            switch($order['status']) {
                                                case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                                case 'confirmed': echo 'bg-blue-100 text-blue-800'; break;
                                                case 'processing': echo 'bg-purple-100 text-purple-800'; break;
                                                case 'shipped': echo 'bg-indigo-100 text-indigo-800'; break;
                                                case 'delivered': echo 'bg-green-100 text-green-800'; break;
                                                case 'cancelled': echo 'bg-red-100 text-red-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800';
                                            }
                                            ?>">
                                            <?php echo ucfirst($order['status']); ?>
                                        </span>
                                        <p class="text-lg font-bold text-gray-900 mt-1">
                                            <?php echo format_currency($order['final_amount']); ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="p-6">
                                <div class="flex justify-between items-center">
                                    <div class="text-sm text-gray-600">
                                        <?php if ($order['shipping_address']): ?>
                                            <p><strong>Shipping Address:</strong> <?php echo htmlspecialchars($order['shipping_address']); ?></p>
                                        <?php endif; ?>
                                        <?php if ($order['tracking_number']): ?>
                                            <p><strong>Tracking Number:</strong> <?php echo htmlspecialchars($order['tracking_number']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <a href="order-view.php?id=<?php echo $order['id']; ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm">
                                            <i class="fas fa-eye mr-1"></i> View Details
                                        </a>
                                        <?php if ($order['status'] === 'pending'): ?>
                                            <button class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 text-sm">
                                                <i class="fas fa-times mr-1"></i> Cancel
                                            </button>
                                        <?php endif; ?>
                                        <?php if (in_array($order['status'], ['delivered'])): ?>
                                            <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm">
                                                <i class="fas fa-redo mr-1"></i> Reorder
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
