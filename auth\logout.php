<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Track logout event if user is logged in
if (is_logged_in()) {
    track_event('user_logout', ['user_id' => $_SESSION['user_id']], $_SESSION['user_id']);
}

// Clear all session data
session_unset();
session_destroy();

// Start a new session for the redirect message
session_start();
$_SESSION['logout_message'] = 'You have been successfully logged out.';

// Redirect to login page
header('Location: login.php');
exit();
?>
