<?php
// Essential functions for Tradexa platform

// Security functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Authentication functions
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

function get_logged_in_user() {
    global $pdo;
    if (!is_logged_in()) return null;

    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetch();
}

function has_role($role) {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === $role;
}

function require_login() {
    if (!is_logged_in()) {
        header('Location: /auth/login.php');
        exit();
    }
}

function require_role($role) {
    require_login();
    if (!has_role($role)) {
        header('Location: /index.php');
        exit();
    }
}

// Include email configuration
require_once __DIR__ . '/../config/email.php';

// Enable development mode (emails will be logged instead of sent)
define('DEVELOPMENT_MODE', true);

// Email functions
function send_email($to, $subject, $message, $headers = '') {
    return send_smtp_email($to, $subject, $message);
}

function send_otp_email($email, $otp, $user_name = '') {
    $template = get_email_template('otp_verification', [
        'name' => $user_name ?: 'User',
        'otp' => $otp
    ]);

    if (!$template) {
        return false;
    }

    return send_smtp_email($email, $template['subject'], $template['body']);
}

function send_welcome_email($email, $user_name, $login_url = '') {
    if (empty($login_url)) {
        $login_url = 'http://' . $_SERVER['HTTP_HOST'] . '/auth/login.php';
    }

    $template = get_email_template('welcome', [
        'name' => $user_name,
        'login_url' => $login_url
    ]);

    if (!$template) {
        return false;
    }

    return send_smtp_email($email, $template['subject'], $template['body']);
}

// OTP functions
function generate_otp() {
    return sprintf("%06d", mt_rand(1, 999999));
}

function save_otp($user_id, $otp) {
    global $pdo;
    $expires = date('Y-m-d H:i:s', strtotime('+10 minutes'));
    
    $stmt = $pdo->prepare("UPDATE users SET otp_code = ?, otp_expires = ? WHERE id = ?");
    return $stmt->execute([$otp, $expires, $user_id]);
}

function verify_otp($user_id, $otp) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT otp_code, otp_expires FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user || $user['otp_code'] !== $otp) {
        return false;
    }
    
    if (strtotime($user['otp_expires']) < time()) {
        return false;
    }
    
    // Clear OTP after successful verification
    $stmt = $pdo->prepare("UPDATE users SET otp_code = NULL, otp_expires = NULL, email_verified = TRUE WHERE id = ?");
    $stmt->execute([$user_id]);
    
    return true;
}

// File upload functions
function upload_file($file, $directory, $allowed_types = ['jpg', 'jpeg', 'png', 'gif']) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'error' => 'No file uploaded'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'error' => 'Invalid file type'];
    }
    
    $max_size = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $max_size) {
        return ['success' => false, 'error' => 'File too large'];
    }
    
    if (!is_dir($directory)) {
        mkdir($directory, 0755, true);
    }
    
    $filename = uniqid() . '.' . $file_extension;
    $filepath = $directory . '/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename, 'filepath' => $filepath];
    }
    
    return ['success' => false, 'error' => 'Upload failed'];
}

// Utility functions
function generate_slug($text) {
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

function format_currency($amount, $currency = 'INR') {
    return $currency . ' ' . number_format($amount, 2);
}

function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    
    return floor($time/31536000) . ' years ago';
}

function get_setting($key, $default = null) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    
    return $result ? $result['setting_value'] : $default;
}

function update_setting($key, $value) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
    return $stmt->execute([$key, $value, $value]);
}

// Pagination function
function paginate($total_records, $records_per_page, $current_page) {
    $total_pages = ceil($total_records / $records_per_page);
    $offset = ($current_page - 1) * $records_per_page;
    
    return [
        'total_pages' => $total_pages,
        'current_page' => $current_page,
        'offset' => $offset,
        'limit' => $records_per_page,
        'has_prev' => $current_page > 1,
        'has_next' => $current_page < $total_pages
    ];
}

// Notification functions
function create_notification($user_id, $type, $title, $message, $data = null) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO notifications (user_id, type, title, message, data) VALUES (?, ?, ?, ?, ?)");
    return $stmt->execute([$user_id, $type, $title, $message, json_encode($data)]);
}

function get_unread_notifications($user_id, $limit = 10) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM notifications WHERE user_id = ? AND is_read = FALSE ORDER BY created_at DESC LIMIT ?");
    $stmt->execute([$user_id, $limit]);
    return $stmt->fetchAll();
}

// Analytics functions
function track_event($event_type, $event_data = null, $user_id = null) {
    global $pdo;
    
    $session_id = session_id();
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referrer = $_SERVER['HTTP_REFERER'] ?? '';
    $page_url = $_SERVER['REQUEST_URI'] ?? '';
    
    $stmt = $pdo->prepare("INSERT INTO analytics_events (user_id, session_id, event_type, event_data, ip_address, user_agent, referrer, page_url) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    return $stmt->execute([$user_id, $session_id, $event_type, json_encode($event_data), $ip_address, $user_agent, $referrer, $page_url]);
}

// Product functions
function get_product_price($product_id, $quantity = 1) {
    global $pdo;
    
    // Get tiered pricing
    $stmt = $pdo->prepare("
        SELECT price FROM product_pricing 
        WHERE product_id = ? AND min_quantity <= ? 
        AND (max_quantity IS NULL OR max_quantity >= ?)
        ORDER BY min_quantity DESC LIMIT 1
    ");
    $stmt->execute([$product_id, $quantity, $quantity]);
    $pricing = $stmt->fetch();
    
    if ($pricing) {
        return $pricing['price'];
    }
    
    // Fallback to base price
    $stmt = $pdo->prepare("SELECT base_price FROM products WHERE id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();
    
    return $product ? $product['base_price'] : 0;
}

// Order functions
function generate_order_number() {
    return 'TRX' . date('Ymd') . sprintf('%06d', mt_rand(1, 999999));
}
?>
