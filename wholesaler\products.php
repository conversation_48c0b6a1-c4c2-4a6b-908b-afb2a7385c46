<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require wholesaler role
require_role('wholesaler');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

// Get user's stores
$stmt = $pdo->prepare("SELECT * FROM stores WHERE wholesaler_id = ? AND status = 'active'");
$stmt->execute([$user_id]);
$stores = $stmt->fetchAll();

// Get selected store
$selected_store_id = $_GET['store_id'] ?? '';
$selected_store = null;
if ($selected_store_id) {
    foreach ($stores as $store) {
        if ($store['id'] == $selected_store_id) {
            $selected_store = $store;
            break;
        }
    }
}

// Get products for selected store
$products = [];
if ($selected_store) {
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name,
               (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.store_id = ? 
        ORDER BY p.created_at DESC
    ");
    $stmt->execute([$selected_store_id]);
    $products = $stmt->fetchAll();
}

// Get categories for dropdown
$stmt = $pdo->query("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
$categories = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - Tradexa Wholesaler</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Wholesaler</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> My Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Products</h2>
                <?php if ($selected_store): ?>
                    <a href="product-add.php?store_id=<?php echo $selected_store_id; ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        <i class="fas fa-plus mr-1"></i> Add Product
                    </a>
                <?php endif; ?>
            </div>

            <?php if (empty($stores)): ?>
                <!-- No Stores -->
                <div class="bg-white rounded-lg shadow p-8 text-center">
                    <i class="fas fa-store text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Active Stores</h3>
                    <p class="text-gray-600 mb-4">You need to create and activate a store before adding products.</p>
                    <a href="stores.php?action=add" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                        Create Your First Store
                    </a>
                </div>
            <?php else: ?>
                <!-- Store Selection -->
                <div class="bg-white rounded-lg shadow p-6 mb-6">
                    <h3 class="text-lg font-semibold mb-4">Select Store</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php foreach ($stores as $store): ?>
                            <a href="?store_id=<?php echo $store['id']; ?>" 
                               class="block p-4 border rounded-lg hover:shadow-lg transition-shadow <?php echo $selected_store_id == $store['id'] ? 'border-blue-500 bg-blue-50' : 'border-gray-200'; ?>">
                                <h4 class="font-semibold"><?php echo htmlspecialchars($store['store_name']); ?></h4>
                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($store['city']); ?></p>
                                <?php if ($selected_store_id == $store['id']): ?>
                                    <span class="inline-block mt-2 text-xs bg-blue-600 text-white px-2 py-1 rounded">Selected</span>
                                <?php endif; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>

                <?php if ($selected_store): ?>
                    <!-- Products List -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg font-medium text-gray-900">
                                    Products for <?php echo htmlspecialchars($selected_store['store_name']); ?>
                                </h3>
                                <span class="text-sm text-gray-500"><?php echo count($products); ?> products</span>
                            </div>
                        </div>
                        <div class="p-6">
                            <?php if (empty($products)): ?>
                                <div class="text-center py-8">
                                    <i class="fas fa-box text-4xl text-gray-400 mb-4"></i>
                                    <p class="text-gray-500 mb-4">No products added yet</p>
                                    <a href="product-add.php?store_id=<?php echo $selected_store_id; ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                        Add Your First Product
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                                    <?php foreach ($products as $product): ?>
                                        <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow">
                                            <?php if ($product['primary_image']): ?>
                                                <img src="../<?php echo $product['primary_image']; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="w-full h-32 object-cover rounded mb-3">
                                            <?php else: ?>
                                                <div class="w-full h-32 bg-gray-200 rounded mb-3 flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400 text-2xl"></i>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <h4 class="font-semibold text-gray-900 mb-1"><?php echo htmlspecialchars($product['name']); ?></h4>
                                            <p class="text-sm text-gray-600 mb-2"><?php echo htmlspecialchars($product['category_name'] ?? 'Uncategorized'); ?></p>
                                            
                                            <div class="flex justify-between items-center mb-2">
                                                <span class="text-lg font-bold text-green-600"><?php echo format_currency($product['base_price']); ?></span>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                    <?php echo $product['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'; ?>">
                                                    <?php echo ucfirst($product['status']); ?>
                                                </span>
                                            </div>
                                            
                                            <div class="text-xs text-gray-500 mb-3">
                                                <p>SKU: <?php echo htmlspecialchars($product['sku']); ?></p>
                                                <p>Stock: <?php echo $product['stock_quantity']; ?> <?php echo $product['unit']; ?></p>
                                                <p>Min Order: <?php echo $product['min_order_quantity']; ?> <?php echo $product['unit']; ?></p>
                                            </div>
                                            
                                            <div class="flex space-x-2">
                                                <a href="product-edit.php?id=<?php echo $product['id']; ?>" class="flex-1 text-center bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700">
                                                    <i class="fas fa-edit mr-1"></i> Edit
                                                </a>
                                                <a href="product-view.php?id=<?php echo $product['id']; ?>" class="flex-1 text-center bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700">
                                                    <i class="fas fa-eye mr-1"></i> View
                                                </a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- No Store Selected -->
                    <div class="bg-white rounded-lg shadow p-8 text-center">
                        <i class="fas fa-mouse-pointer text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Select a Store</h3>
                        <p class="text-gray-600">Choose a store from above to view and manage its products.</p>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
