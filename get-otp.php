<?php
// Simple page to get <PERSON><PERSON> from email log for testing
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get OTP - Tradexa</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4">
        <div class="max-w-2xl w-full">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold text-center mb-6">Get Your OTP Code</h2>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <p class="text-blue-800">
                        <i class="fas fa-info-circle mr-2"></i>
                        Since we're in development mode, emails are logged instead of sent. 
                        Your OTP code will appear below.
                    </p>
                </div>
                
                <?php
                $log_file = 'logs/emails.log';
                if (file_exists($log_file)) {
                    $log_content = file_get_contents($log_file);
                    
                    // Extract the most recent OTP
                    if (preg_match_all('/(\d{6})/', $log_content, $matches)) {
                        $latest_otp = end($matches[1]);
                        echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6 text-center'>";
                        echo "<h3 class='text-lg font-semibold text-green-800 mb-2'>Your Latest OTP Code:</h3>";
                        echo "<div class='text-3xl font-bold text-green-600 tracking-widest'>$latest_otp</div>";
                        echo "<p class='text-sm text-green-700 mt-2'>Use this code to verify your email</p>";
                        echo "</div>";
                    } else {
                        echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
                        echo "<p class='text-yellow-800'>No OTP found in logs. Please try registering first.</p>";
                        echo "</div>";
                    }
                    
                    echo "<div class='mt-6'>";
                    echo "<h4 class='font-semibold mb-2'>Full Email Log:</h4>";
                    echo "<div class='bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto'>";
                    echo "<pre class='text-sm'>" . htmlspecialchars($log_content) . "</pre>";
                    echo "</div>";
                    echo "</div>";
                } else {
                    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>";
                    echo "<p class='text-red-800'>No email log found. Please try registering first.</p>";
                    echo "</div>";
                }
                ?>
                
                <div class="mt-6 text-center space-x-4">
                    <a href="auth/register.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        Register New Account
                    </a>
                    <a href="auth/verify-email.php" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                        Verify Email
                    </a>
                    <button onclick="location.reload()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                        Refresh
                    </button>
                </div>
                
                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-600">
                        To enable actual email sending, set DEVELOPMENT_MODE to false in includes/functions.php
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
