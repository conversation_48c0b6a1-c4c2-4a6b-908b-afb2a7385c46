// Tradexa Platform JavaScript Functions

// Utility functions
const Utils = {
    // Show notification
    showNotification: function(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after duration
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    },

    // Format currency
    formatCurrency: function(amount, currency = 'INR') {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },

    // Format number
    formatNumber: function(number) {
        return new Intl.NumberFormat('en-IN').format(number);
    },

    // Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // AJAX helper
    ajax: function(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const config = Object.assign(defaults, options);
        
        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('AJAX Error:', error);
                throw error;
            });
    }
};

// Search functionality
const Search = {
    init: function() {
        const searchInput = document.getElementById('search-input');
        const searchSuggestions = document.getElementById('search-suggestions');
        
        if (searchInput) {
            const debouncedSearch = Utils.debounce(this.performSearch.bind(this), 300);
            searchInput.addEventListener('input', debouncedSearch);
            
            // Hide suggestions when clicking outside
            document.addEventListener('click', (e) => {
                if (!searchInput.contains(e.target) && !searchSuggestions?.contains(e.target)) {
                    if (searchSuggestions) {
                        searchSuggestions.style.display = 'none';
                    }
                }
            });
        }
    },

    performSearch: function(event) {
        const query = event.target.value.trim();
        const searchSuggestions = document.getElementById('search-suggestions');
        
        if (query.length < 2) {
            if (searchSuggestions) {
                searchSuggestions.style.display = 'none';
            }
            return;
        }

        // Show loading
        if (searchSuggestions) {
            searchSuggestions.innerHTML = '<div class="search-suggestion-item">Searching...</div>';
            searchSuggestions.style.display = 'block';
        }

        // Perform search
        Utils.ajax(`/api/search-suggestions.php?q=${encodeURIComponent(query)}`)
            .then(data => {
                this.displaySuggestions(data.suggestions || []);
            })
            .catch(error => {
                console.error('Search error:', error);
                if (searchSuggestions) {
                    searchSuggestions.style.display = 'none';
                }
            });
    },

    displaySuggestions: function(suggestions) {
        const searchSuggestions = document.getElementById('search-suggestions');
        
        if (!searchSuggestions) return;

        if (suggestions.length === 0) {
            searchSuggestions.innerHTML = '<div class="search-suggestion-item">No suggestions found</div>';
            return;
        }

        const html = suggestions.map(suggestion => 
            `<div class="search-suggestion-item" onclick="Search.selectSuggestion('${suggestion.value}')">
                <i class="fas fa-${suggestion.type === 'product' ? 'box' : 'store'} mr-2"></i>
                ${suggestion.label}
            </div>`
        ).join('');

        searchSuggestions.innerHTML = html;
        searchSuggestions.style.display = 'block';
    },

    selectSuggestion: function(value) {
        const searchInput = document.getElementById('search-input');
        const searchSuggestions = document.getElementById('search-suggestions');
        
        if (searchInput) {
            searchInput.value = value;
        }
        
        if (searchSuggestions) {
            searchSuggestions.style.display = 'none';
        }
        
        // Trigger search
        this.submitSearch();
    },

    submitSearch: function() {
        const searchInput = document.getElementById('search-input');
        if (searchInput && searchInput.value.trim()) {
            window.location.href = `/search.php?q=${encodeURIComponent(searchInput.value.trim())}`;
        }
    }
};

// Cart functionality
const Cart = {
    init: function() {
        this.updateCartCount();
        this.bindEvents();
    },

    bindEvents: function() {
        // Add to cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('add-to-cart-btn')) {
                e.preventDefault();
                const productId = e.target.dataset.productId;
                const quantity = e.target.dataset.quantity || 1;
                this.addToCart(productId, quantity);
            }
        });

        // Remove from cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-from-cart-btn')) {
                e.preventDefault();
                const cartItemId = e.target.dataset.cartItemId;
                this.removeFromCart(cartItemId);
            }
        });

        // Update quantity
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('cart-quantity-input')) {
                const cartItemId = e.target.dataset.cartItemId;
                const quantity = parseInt(e.target.value);
                this.updateQuantity(cartItemId, quantity);
            }
        });
    },

    addToCart: function(productId, quantity = 1) {
        const btn = document.querySelector(`[data-product-id="${productId}"]`);
        const originalText = btn ? btn.innerHTML : '';
        
        if (btn) {
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            btn.disabled = true;
        }

        Utils.ajax('/api/cart.php', {
            method: 'POST',
            body: JSON.stringify({
                action: 'add',
                product_id: productId,
                quantity: quantity
            })
        })
        .then(data => {
            if (data.success) {
                Utils.showNotification('Product added to cart!', 'success');
                this.updateCartCount();
            } else {
                Utils.showNotification(data.message || 'Failed to add product to cart', 'error');
            }
        })
        .catch(error => {
            Utils.showNotification('Error adding product to cart', 'error');
        })
        .finally(() => {
            if (btn) {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        });
    },

    removeFromCart: function(cartItemId) {
        if (!confirm('Remove this item from cart?')) return;

        Utils.ajax('/api/cart.php', {
            method: 'POST',
            body: JSON.stringify({
                action: 'remove',
                cart_item_id: cartItemId
            })
        })
        .then(data => {
            if (data.success) {
                Utils.showNotification('Item removed from cart', 'success');
                this.updateCartCount();
                // Remove item from DOM
                const itemElement = document.querySelector(`[data-cart-item-id="${cartItemId}"]`).closest('.cart-item');
                if (itemElement) {
                    itemElement.remove();
                }
            } else {
                Utils.showNotification(data.message || 'Failed to remove item', 'error');
            }
        })
        .catch(error => {
            Utils.showNotification('Error removing item from cart', 'error');
        });
    },

    updateQuantity: function(cartItemId, quantity) {
        if (quantity < 1) return;

        Utils.ajax('/api/cart.php', {
            method: 'POST',
            body: JSON.stringify({
                action: 'update',
                cart_item_id: cartItemId,
                quantity: quantity
            })
        })
        .then(data => {
            if (data.success) {
                this.updateCartCount();
                // Update total price in DOM if exists
                const totalElement = document.querySelector(`[data-cart-item-id="${cartItemId}"] .item-total`);
                if (totalElement && data.item_total) {
                    totalElement.textContent = Utils.formatCurrency(data.item_total);
                }
            } else {
                Utils.showNotification(data.message || 'Failed to update quantity', 'error');
            }
        })
        .catch(error => {
            Utils.showNotification('Error updating quantity', 'error');
        });
    },

    updateCartCount: function() {
        Utils.ajax('/api/cart.php?action=count')
            .then(data => {
                const cartCountElements = document.querySelectorAll('.cart-count');
                cartCountElements.forEach(element => {
                    element.textContent = data.count || 0;
                    element.style.display = data.count > 0 ? 'inline' : 'none';
                });
            })
            .catch(error => {
                console.error('Error updating cart count:', error);
            });
    }
};

// File upload functionality
const FileUpload = {
    init: function() {
        this.bindEvents();
    },

    bindEvents: function() {
        // File input change
        document.addEventListener('change', (e) => {
            if (e.target.type === 'file' && e.target.classList.contains('image-upload')) {
                this.previewImage(e.target);
            }
        });

        // Drag and drop
        document.addEventListener('dragover', (e) => {
            if (e.target.classList.contains('file-upload-area')) {
                e.preventDefault();
                e.target.classList.add('dragover');
            }
        });

        document.addEventListener('dragleave', (e) => {
            if (e.target.classList.contains('file-upload-area')) {
                e.target.classList.remove('dragover');
            }
        });

        document.addEventListener('drop', (e) => {
            if (e.target.classList.contains('file-upload-area')) {
                e.preventDefault();
                e.target.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                const fileInput = e.target.querySelector('input[type="file"]');
                if (fileInput && files.length > 0) {
                    fileInput.files = files;
                    this.previewImage(fileInput);
                }
            }
        });
    },

    previewImage: function(input) {
        const file = input.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            Utils.showNotification('Please select an image file', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            const previewId = input.dataset.preview;
            const preview = document.getElementById(previewId);
            
            if (preview) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }
        };
        
        reader.readAsDataURL(file);
    }
};

// Form validation
const FormValidation = {
    init: function() {
        this.bindEvents();
    },

    bindEvents: function() {
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('validate-form')) {
                if (!this.validateForm(e.target)) {
                    e.preventDefault();
                }
            }
        });

        // Real-time validation
        document.addEventListener('blur', (e) => {
            if (e.target.hasAttribute('data-validate')) {
                this.validateField(e.target);
            }
        });
    },

    validateForm: function(form) {
        let isValid = true;
        const fields = form.querySelectorAll('[data-validate]');
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    },

    validateField: function(field) {
        const rules = field.dataset.validate.split('|');
        let isValid = true;
        let errorMessage = '';

        for (const rule of rules) {
            const [ruleName, ruleValue] = rule.split(':');
            
            switch (ruleName) {
                case 'required':
                    if (!field.value.trim()) {
                        isValid = false;
                        errorMessage = 'This field is required';
                    }
                    break;
                    
                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (field.value && !emailRegex.test(field.value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address';
                    }
                    break;
                    
                case 'min':
                    if (field.value.length < parseInt(ruleValue)) {
                        isValid = false;
                        errorMessage = `Minimum ${ruleValue} characters required`;
                    }
                    break;
                    
                case 'max':
                    if (field.value.length > parseInt(ruleValue)) {
                        isValid = false;
                        errorMessage = `Maximum ${ruleValue} characters allowed`;
                    }
                    break;
            }
            
            if (!isValid) break;
        }

        // Update field appearance
        field.classList.remove('form-error', 'form-success');
        field.classList.add(isValid ? 'form-success' : 'form-error');

        // Show/hide error message
        const errorElement = field.parentElement.querySelector('.error-message');
        if (errorElement) {
            errorElement.textContent = errorMessage;
            errorElement.style.display = errorMessage ? 'block' : 'none';
        }

        return isValid;
    }
};

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    Search.init();
    Cart.init();
    FileUpload.init();
    FormValidation.init();
});

// Export for use in other scripts
window.Tradexa = {
    Utils,
    Search,
    Cart,
    FileUpload,
    FormValidation
};
