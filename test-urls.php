<?php
// Test page to check URL functionality
session_start();

echo "<h1>URL Test Page</h1>";

// Test database connection
try {
    require_once 'config/database.php';
    echo "<p style='color: green;'>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test functions include
try {
    require_once 'includes/functions.php';
    echo "<p style='color: green;'>✅ Functions included successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Functions include failed: " . $e->getMessage() . "</p>";
}

// Check if user is logged in
if (isset($_SESSION['user_id'])) {
    echo "<p style='color: green;'>✅ User is logged in (ID: " . $_SESSION['user_id'] . ")</p>";
    
    try {
        $user = get_logged_in_user();
        echo "<p style='color: green;'>✅ User data retrieved: " . $user['first_name'] . " " . $user['last_name'] . " (" . $user['role'] . ")</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Failed to get user data: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ User is not logged in</p>";
}

// Test URLs
echo "<h2>Test URLs</h2>";

$urls_to_test = [
    'Admin Dashboard' => 'admin/dashboard.php',
    'Admin Users' => 'admin/users.php',
    'Admin Stores' => 'admin/stores.php',
    'Wholesaler Dashboard' => 'wholesaler/dashboard.php',
    'Wholesaler Stores' => 'wholesaler/stores.php',
    'Retailer Dashboard' => 'retailer/dashboard.php',
    'Retailer Browse' => 'retailer/browse.php',
    'Login Page' => 'auth/login.php',
    'Register Page' => 'auth/register.php'
];

echo "<ul>";
foreach ($urls_to_test as $name => $url) {
    $full_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/" . $url;
    echo "<li><a href='$url' target='_blank'>$name</a> - <code>$url</code></li>";
}
echo "</ul>";

// Check file existence
echo "<h2>File Existence Check</h2>";
echo "<ul>";
foreach ($urls_to_test as $name => $url) {
    if (file_exists($url)) {
        echo "<li style='color: green;'>✅ $name - $url exists</li>";
    } else {
        echo "<li style='color: red;'>❌ $name - $url missing</li>";
    }
}
echo "</ul>";

// Check database tables
echo "<h2>Database Tables Check</h2>";
try {
    $tables = ['users', 'user_profiles', 'stores', 'products', 'categories', 'orders', 'order_items'];
    echo "<ul>";
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<li style='color: green;'>✅ Table '$table' exists</li>";
        } else {
            echo "<li style='color: red;'>❌ Table '$table' missing</li>";
        }
    }
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database table check failed: " . $e->getMessage() . "</p>";
}

echo "<h2>PHP Info</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Server: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>Request URI: " . $_SERVER['REQUEST_URI'] . "</p>";
?>
