<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin role
require_role('admin');

$current_user = get_logged_in_user();

// Get products with pagination
$page = max(1, $_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

$status_filter = $_GET['status'] ?? '';
$category_filter = $_GET['category'] ?? '';
$search = $_GET['search'] ?? '';

$where_conditions = ["1=1"];
$params = [];

if ($status_filter) {
    $where_conditions[] = "p.status = ?";
    $params[] = $status_filter;
}

if ($category_filter) {
    $where_conditions[] = "p.category_id = ?";
    $params[] = $category_filter;
}

if ($search) {
    $where_conditions[] = "(p.name LIKE ? OR p.sku LIKE ? OR s.store_name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count
$count_sql = "SELECT COUNT(*) as total FROM products p 
              JOIN stores s ON p.store_id = s.id 
              WHERE $where_clause";
$stmt = $pdo->prepare($count_sql);
$stmt->execute($params);
$total_products = $stmt->fetch()['total'];

// Get products
$sql = "SELECT p.*, s.store_name, c.name as category_name,
               (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
        FROM products p 
        JOIN stores s ON p.store_id = s.id 
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE $where_clause 
        ORDER BY p.created_at DESC 
        LIMIT $limit OFFSET $offset";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$products = $stmt->fetchAll();

// Get categories for filter
$stmt = $pdo->query("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
$categories = $stmt->fetchAll();

$total_pages = ceil($total_products / $limit);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - Tradexa Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Admin</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="users.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-users mr-3"></i> Users
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="categories.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tags mr-3"></i> Categories
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="settings.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-cog mr-3"></i> Settings
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Products Management</h2>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <form method="GET" class="flex flex-wrap gap-4">
                    <div>
                        <input type="text" name="search" placeholder="Search products..." value="<?php echo htmlspecialchars($search); ?>" 
                               class="border border-gray-300 rounded-lg px-3 py-2">
                    </div>
                    <div>
                        <select name="category" class="border border-gray-300 rounded-lg px-3 py-2">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>" <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <select name="status" class="border border-gray-300 rounded-lg px-3 py-2">
                            <option value="">All Status</option>
                            <option value="draft" <?php echo $status_filter === 'draft' ? 'selected' : ''; ?>>Draft</option>
                            <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            <option value="out_of_stock" <?php echo $status_filter === 'out_of_stock' ? 'selected' : ''; ?>>Out of Stock</option>
                        </select>
                    </div>
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        <i class="fas fa-search mr-1"></i> Filter
                    </button>
                    <a href="products.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                        Clear
                    </a>
                </form>
            </div>

            <!-- Products Grid -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        Products (<?php echo number_format($total_products); ?> total)
                    </h3>
                </div>
                <div class="p-6">
                    <?php if (empty($products)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-box text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500">No products found</p>
                        </div>
                    <?php else: ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            <?php foreach ($products as $product): ?>
                                <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow">
                                    <?php if ($product['primary_image']): ?>
                                        <img src="../<?php echo $product['primary_image']; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="w-full h-32 object-cover rounded mb-3">
                                    <?php else: ?>
                                        <div class="w-full h-32 bg-gray-200 rounded mb-3 flex items-center justify-center">
                                            <i class="fas fa-image text-gray-400 text-2xl"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <h4 class="font-semibold text-gray-900 mb-1"><?php echo htmlspecialchars($product['name']); ?></h4>
                                    <p class="text-sm text-gray-600 mb-1">Store: <?php echo htmlspecialchars($product['store_name']); ?></p>
                                    <p class="text-sm text-gray-600 mb-2">Category: <?php echo htmlspecialchars($product['category_name'] ?? 'Uncategorized'); ?></p>
                                    
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-lg font-bold text-green-600"><?php echo format_currency($product['base_price']); ?></span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            <?php 
                                            switch($product['status']) {
                                                case 'active': echo 'bg-green-100 text-green-800'; break;
                                                case 'draft': echo 'bg-yellow-100 text-yellow-800'; break;
                                                case 'inactive': echo 'bg-red-100 text-red-800'; break;
                                                case 'out_of_stock': echo 'bg-gray-100 text-gray-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800';
                                            }
                                            ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $product['status'])); ?>
                                        </span>
                                    </div>
                                    
                                    <div class="text-xs text-gray-500 mb-3">
                                        <p>SKU: <?php echo htmlspecialchars($product['sku']); ?></p>
                                        <p>Stock: <?php echo $product['stock_quantity']; ?> <?php echo $product['unit']; ?></p>
                                        <p>Min Order: <?php echo $product['min_order_quantity']; ?> <?php echo $product['unit']; ?></p>
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <a href="product-view.php?id=<?php echo $product['id']; ?>" class="flex-1 text-center bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700">
                                            <i class="fas fa-eye mr-1"></i> View
                                        </a>
                                        <a href="product-edit.php?id=<?php echo $product['id']; ?>" class="flex-1 text-center bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700">
                                            <i class="fas fa-edit mr-1"></i> Edit
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-700">
                                Showing <?php echo $offset + 1; ?> to <?php echo min($offset + $limit, $total_products); ?> of <?php echo $total_products; ?> results
                            </div>
                            <div class="flex space-x-1">
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                    <a href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&category=<?php echo $category_filter; ?>&search=<?php echo urlencode($search); ?>" 
                                       class="px-3 py-2 text-sm <?php echo $i === $page ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?> rounded">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
