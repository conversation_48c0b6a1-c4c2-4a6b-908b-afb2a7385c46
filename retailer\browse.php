<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require retailer role
require_role('retailer');

$current_user = get_logged_in_user();

// Get search parameters
$search = $_GET['search'] ?? '';
$category_id = $_GET['category'] ?? '';
$min_price = $_GET['min_price'] ?? '';
$max_price = $_GET['max_price'] ?? '';
$sort = $_GET['sort'] ?? 'newest';

// Build query
$where_conditions = ["p.status = 'active'", "s.status = 'active'"];
$params = [];

if ($search) {
    $where_conditions[] = "(p.name LIKE ? OR p.description LIKE ? OR s.store_name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($category_id) {
    $where_conditions[] = "p.category_id = ?";
    $params[] = $category_id;
}

if ($min_price) {
    $where_conditions[] = "p.base_price >= ?";
    $params[] = $min_price;
}

if ($max_price) {
    $where_conditions[] = "p.base_price <= ?";
    $params[] = $max_price;
}

$where_clause = implode(' AND ', $where_conditions);

// Sort options
$order_clause = match($sort) {
    'price_low' => 'p.base_price ASC',
    'price_high' => 'p.base_price DESC',
    'name' => 'p.name ASC',
    'newest' => 'p.created_at DESC',
    default => 'p.created_at DESC'
};

// Get products
$sql = "SELECT p.*, s.store_name, s.city, c.name as category_name,
               (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
        FROM products p 
        JOIN stores s ON p.store_id = s.id 
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE $where_clause 
        ORDER BY $order_clause
        LIMIT 50";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$products = $stmt->fetchAll();

// Get categories for filter
$stmt = $pdo->query("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
$categories = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browse Products - Tradexa</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="browse.php" class="text-gray-700 hover:text-blue-600">Browse Products</a>
                    <a href="cart.php" class="text-gray-700 hover:text-blue-600">
                        <i class="fas fa-shopping-cart"></i> Cart
                    </a>
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="browse.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-search mr-3"></i> Browse Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> My Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="wishlist.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-heart mr-3"></i> Wishlist
                        </a>
                    </li>
                    <li>
                        <a href="suppliers.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Followed Suppliers
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <!-- Search and Filters -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <form method="GET" class="space-y-4">
                    <div class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input type="text" name="search" placeholder="Search products..." value="<?php echo htmlspecialchars($search); ?>" 
                                   class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                            <i class="fas fa-search mr-1"></i> Search
                        </button>
                    </div>
                    
                    <div class="flex flex-wrap gap-4">
                        <div>
                            <select name="category" class="border border-gray-300 rounded-lg px-3 py-2">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo $category_id == $category['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div>
                            <input type="number" name="min_price" placeholder="Min Price" value="<?php echo htmlspecialchars($min_price); ?>" 
                                   class="border border-gray-300 rounded-lg px-3 py-2 w-32">
                        </div>
                        <div>
                            <input type="number" name="max_price" placeholder="Max Price" value="<?php echo htmlspecialchars($max_price); ?>" 
                                   class="border border-gray-300 rounded-lg px-3 py-2 w-32">
                        </div>
                        <div>
                            <select name="sort" class="border border-gray-300 rounded-lg px-3 py-2">
                                <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>>Newest First</option>
                                <option value="price_low" <?php echo $sort === 'price_low' ? 'selected' : ''; ?>>Price: Low to High</option>
                                <option value="price_high" <?php echo $sort === 'price_high' ? 'selected' : ''; ?>>Price: High to Low</option>
                                <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                            </select>
                        </div>
                        <a href="browse.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                            Clear Filters
                        </a>
                    </div>
                </form>
            </div>

            <!-- Products Grid -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        Products (<?php echo count($products); ?> found)
                    </h3>
                </div>
                <div class="p-6">
                    <?php if (empty($products)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500">No products found matching your criteria</p>
                        </div>
                    <?php else: ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            <?php foreach ($products as $product): ?>
                                <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow product-card">
                                    <?php if ($product['primary_image']): ?>
                                        <img src="../<?php echo $product['primary_image']; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="w-full h-32 object-cover rounded mb-3">
                                    <?php else: ?>
                                        <div class="w-full h-32 bg-gray-200 rounded mb-3 flex items-center justify-center">
                                            <i class="fas fa-image text-gray-400 text-2xl"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <h4 class="font-semibold text-gray-900 mb-1"><?php echo htmlspecialchars($product['name']); ?></h4>
                                    <p class="text-sm text-gray-600 mb-1"><?php echo htmlspecialchars($product['store_name']); ?>, <?php echo htmlspecialchars($product['city']); ?></p>
                                    <p class="text-sm text-gray-600 mb-2"><?php echo htmlspecialchars($product['category_name'] ?? 'Uncategorized'); ?></p>
                                    
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-lg font-bold text-green-600"><?php echo format_currency($product['base_price']); ?></span>
                                        <button class="text-red-500 hover:text-red-700">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                    
                                    <div class="text-xs text-gray-500 mb-3">
                                        <p>Min Order: <?php echo $product['min_order_quantity']; ?> <?php echo $product['unit']; ?></p>
                                        <p>Stock: <?php echo $product['stock_quantity']; ?> <?php echo $product['unit']; ?></p>
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <button class="flex-1 bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 add-to-cart-btn" 
                                                data-product-id="<?php echo $product['id']; ?>" data-quantity="<?php echo $product['min_order_quantity']; ?>">
                                            <i class="fas fa-cart-plus mr-1"></i> Add to Cart
                                        </button>
                                        <button class="flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700">
                                            <i class="fas fa-quote-left mr-1"></i> RFQ
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/app.js"></script>
</body>
</html>
