<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin role
require_role('admin');

$current_user = get_logged_in_user();
$product_id = $_GET['id'] ?? 0;

if (!$product_id) {
    header('Location: products.php');
    exit();
}

// Get product details
$stmt = $pdo->prepare("
    SELECT p.*, s.store_name, s.city, s.state, c.name as category_name,
           u.first_name, u.last_name, u.email
    FROM products p
    JOIN stores s ON p.store_id = s.id
    JOIN users u ON s.wholesaler_id = u.id
    LEFT JOIN categories c ON p.category_id = c.id
    WHERE p.id = ?
");
$stmt->execute([$product_id]);
$product = $stmt->fetch();

if (!$product) {
    header('Location: products.php');
    exit();
}

// Get product images
$stmt = $pdo->prepare("SELECT * FROM product_images WHERE product_id = ? ORDER BY is_primary DESC, id ASC");
$stmt->execute([$product_id]);
$images = $stmt->fetchAll();

// Get pricing tiers
$stmt = $pdo->prepare("SELECT * FROM product_pricing WHERE product_id = ? ORDER BY min_quantity ASC");
$stmt->execute([$product_id]);
$pricing_tiers = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Details - Tradexa Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Admin</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="users.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-users mr-3"></i> Users
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="categories.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tags mr-3"></i> Categories
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="settings.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-cog mr-3"></i> Settings
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Product Details</h2>
                <a href="products.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Products
                </a>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Product Images -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Product Images</h3>
                    </div>
                    <div class="p-6">
                        <?php if (!empty($images)): ?>
                            <div class="grid grid-cols-2 gap-4">
                                <?php foreach ($images as $image): ?>
                                    <div class="relative">
                                        <img src="../<?php echo $image['image_path']; ?>" alt="Product Image" class="w-full h-32 object-cover rounded">
                                        <?php if ($image['is_primary']): ?>
                                            <span class="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">Primary</span>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-8">
                                <i class="fas fa-image text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-500">No images uploaded</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Product Information -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Product Information</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Product Name</label>
                                <p class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($product['name']); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">SKU</label>
                                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($product['sku']); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Category</label>
                                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($product['category_name'] ?? 'Uncategorized'); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Store</label>
                                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($product['store_name']); ?></p>
                                <p class="text-xs text-gray-600"><?php echo htmlspecialchars($product['city'] . ', ' . $product['state']); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Wholesaler</label>
                                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($product['first_name'] . ' ' . $product['last_name']); ?></p>
                                <p class="text-xs text-gray-600"><?php echo htmlspecialchars($product['email']); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Status</label>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    <?php echo $product['status'] === 'active' ? 'bg-green-100 text-green-800' : 
                                        ($product['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'); ?>">
                                    <?php echo ucfirst($product['status']); ?>
                                </span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Base Price</label>
                                <p class="text-lg font-bold text-green-600"><?php echo format_currency($product['base_price']); ?></p>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Min Order Quantity</label>
                                    <p class="text-sm text-gray-900"><?php echo number_format($product['min_order_quantity']); ?> <?php echo $product['unit']; ?></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Stock Quantity</label>
                                    <p class="text-sm text-gray-900"><?php echo number_format($product['stock_quantity']); ?> <?php echo $product['unit']; ?></p>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Description</label>
                                <p class="text-sm text-gray-900"><?php echo nl2br(htmlspecialchars($product['description'])); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Created</label>
                                <p class="text-sm text-gray-900"><?php echo date('M d, Y H:i', strtotime($product['created_at'])); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing Tiers -->
            <?php if (!empty($pricing_tiers)): ?>
                <div class="bg-white rounded-lg shadow mt-6">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Pricing Tiers</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Min Quantity</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max Quantity</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price per Unit</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($pricing_tiers as $tier): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo number_format($tier['min_quantity']); ?> <?php echo $product['unit']; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo $tier['max_quantity'] ? number_format($tier['max_quantity']) . ' ' . $product['unit'] : 'No limit'; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                <?php echo format_currency($tier['price']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php 
                                                $discount = (($product['base_price'] - $tier['price']) / $product['base_price']) * 100;
                                                echo $discount > 0 ? number_format($discount, 1) . '%' : 'No discount';
                                                ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
