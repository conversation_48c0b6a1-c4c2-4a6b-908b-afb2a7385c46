<?php
// Test email functionality
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Email Test - Tradexa</title>
    <script src='https://cdn.tailwindcss.com'></script>
</head>
<body class='bg-gray-50 p-8'>
    <div class='max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6'>";

echo "<h2 class='text-2xl font-bold mb-4'>Testing Email Functionality</h2>";

// Test OTP email
$test_email = "<EMAIL>"; // Send test to your own email
$test_otp = "123456";
$test_name = "Test User";

echo "<div class='mb-4 p-4 bg-blue-50 rounded-lg'>";
echo "<p class='text-blue-800'>Attempting to send OTP email to: <strong>$test_email</strong></p>";
echo "<p class='text-blue-600'>Test OTP: <strong>$test_otp</strong></p>";
echo "</div>";

$result = send_otp_email($test_email, $test_otp, $test_name);

if ($result) {
    echo "<div class='mb-4 p-4 bg-green-50 border border-green-200 rounded-lg'>";
    echo "<p class='text-green-800'>✅ <strong>Email sent successfully!</strong></p>";
    echo "<p class='text-green-600'>Check your email inbox for the OTP: <strong>$test_otp</strong></p>";
    echo "</div>";
} else {
    echo "<div class='mb-4 p-4 bg-red-50 border border-red-200 rounded-lg'>";
    echo "<p class='text-red-800'>❌ <strong>Email sending failed.</strong></p>";
    echo "<p class='text-red-600'>Check the debug logs below for details.</p>";
    echo "</div>";
}

// Show SMTP debug log
$debug_log_file = 'logs/smtp_debug.log';
if (file_exists($debug_log_file)) {
    echo "<div class='mb-4'>";
    echo "<h3 class='text-lg font-semibold mb-2'>SMTP Debug Log:</h3>";
    echo "<div class='bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto'>";
    echo "<pre class='text-sm'>" . htmlspecialchars(file_get_contents($debug_log_file)) . "</pre>";
    echo "</div>";
    echo "</div>";
}

// Show email log if it exists
$log_file = 'logs/emails.log';
if (file_exists($log_file)) {
    echo "<div class='mb-4'>";
    echo "<h3 class='text-lg font-semibold mb-2'>Email Log Content:</h3>";
    echo "<div class='bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto'>";
    echo "<pre class='text-sm'>" . htmlspecialchars(file_get_contents($log_file)) . "</pre>";
    echo "</div>";
    echo "</div>";
}

echo "<div class='mt-6 space-x-4'>";
echo "<a href='auth/register.php' class='bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700'>← Back to Registration</a>";
echo "<button onclick='location.reload()' class='bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700'>Test Again</button>";
echo "</div>";

echo "</div></body></html>";
?>
