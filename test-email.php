<?php
// Test email functionality
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>Testing Email Functionality</h2>";

// Test OTP email
$test_email = "<EMAIL>"; // Send test to your own email
$test_otp = "123456";
$test_name = "Test User";

echo "<p>Attempting to send OTP email to: $test_email</p>";

$result = send_otp_email($test_email, $test_otp, $test_name);

if ($result) {
    echo "<p style='color: green;'>✅ Email sent successfully!</p>";
    echo "<p>Check your email inbox for the OTP: <strong>$test_otp</strong></p>";
} else {
    echo "<p style='color: red;'>❌ Email sending failed.</p>";
    echo "<p>Check the logs/emails.log file for the email content.</p>";
}

// Show email log if it exists
$log_file = 'logs/emails.log';
if (file_exists($log_file)) {
    echo "<h3>Email Log Content:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars(file_get_contents($log_file));
    echo "</pre>";
}

echo "<p><a href='auth/register.php'>← Back to Registration</a></p>";
?>
