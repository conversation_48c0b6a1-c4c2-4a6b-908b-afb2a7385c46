<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Require login
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit();
}

$user_id = $_SESSION['user_id'];
$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'follow':
                $store_id = $input['store_id'] ?? 0;
                
                if (!$store_id) {
                    throw new Exception('Store ID is required');
                }
                
                // Check if store exists
                $stmt = $pdo->prepare("SELECT id FROM stores WHERE id = ? AND status = 'active'");
                $stmt->execute([$store_id]);
                if (!$stmt->fetch()) {
                    throw new Exception('Store not found');
                }
                
                // Follow store (ignore if already following due to UNIQUE constraint)
                $stmt = $pdo->prepare("INSERT IGNORE INTO store_followers (user_id, store_id) VALUES (?, ?)");
                $stmt->execute([$user_id, $store_id]);
                
                echo json_encode(['success' => true, 'message' => 'Following supplier']);
                break;
                
            case 'unfollow':
                $store_id = $input['store_id'] ?? 0;
                
                if (!$store_id) {
                    throw new Exception('Store ID is required');
                }
                
                $stmt = $pdo->prepare("DELETE FROM store_followers WHERE user_id = ? AND store_id = ?");
                $stmt->execute([$user_id, $store_id]);
                
                echo json_encode(['success' => true, 'message' => 'Unfollowed supplier']);
                break;
                
            case 'toggle':
                $store_id = $input['store_id'] ?? 0;
                
                if (!$store_id) {
                    throw new Exception('Store ID is required');
                }
                
                // Check if already following
                $stmt = $pdo->prepare("SELECT id FROM store_followers WHERE user_id = ? AND store_id = ?");
                $stmt->execute([$user_id, $store_id]);
                $exists = $stmt->fetch();
                
                if ($exists) {
                    // Unfollow
                    $stmt = $pdo->prepare("DELETE FROM store_followers WHERE user_id = ? AND store_id = ?");
                    $stmt->execute([$user_id, $store_id]);
                    $message = 'Unfollowed supplier';
                    $following = false;
                } else {
                    // Follow
                    $stmt = $pdo->prepare("INSERT INTO store_followers (user_id, store_id) VALUES (?, ?)");
                    $stmt->execute([$user_id, $store_id]);
                    $message = 'Following supplier';
                    $following = true;
                }
                
                echo json_encode([
                    'success' => true, 
                    'message' => $message,
                    'following' => $following
                ]);
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } elseif ($method === 'GET') {
        // Get followed suppliers
        $stmt = $pdo->prepare("
            SELECT sf.*, s.store_name, s.description, s.city, s.state,
                   u.first_name, u.last_name,
                   (SELECT COUNT(*) FROM products WHERE store_id = s.id AND status = 'active') as product_count
            FROM store_followers sf
            JOIN stores s ON sf.store_id = s.id
            JOIN users u ON s.wholesaler_id = u.id
            WHERE sf.user_id = ? AND s.status = 'active'
            ORDER BY sf.followed_at DESC
        ");
        $stmt->execute([$user_id]);
        $suppliers = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'suppliers' => $suppliers,
            'count' => count($suppliers)
        ]);
        
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
