<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require retailer role
require_role('retailer');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

// Get retailer statistics
$stats = [];

// Total orders
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM orders WHERE retailer_id = ?");
$stmt->execute([$user_id]);
$stats['total_orders'] = $stmt->fetch()['total'];

// Pending orders
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM orders WHERE retailer_id = ? AND status IN ('pending', 'confirmed', 'processing')");
$stmt->execute([$user_id]);
$stats['pending_orders'] = $stmt->fetch()['total'];

// Wishlist items
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM wishlists WHERE user_id = ?");
$stmt->execute([$user_id]);
$stats['wishlist_items'] = $stmt->fetch()['total'];

// RFQ requests
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM rfq_requests WHERE retailer_id = ?");
$stmt->execute([$user_id]);
$stats['rfq_requests'] = $stmt->fetch()['total'];

// Recent orders
$stmt = $pdo->prepare("
    SELECT o.*, 
           (SELECT COUNT(*) FROM order_items WHERE order_id = o.id) as item_count
    FROM orders o 
    WHERE o.retailer_id = ? 
    ORDER BY o.created_at DESC 
    LIMIT 5
");
$stmt->execute([$user_id]);
$recent_orders = $stmt->fetchAll();

// Recent RFQs
$stmt = $pdo->prepare("
    SELECT r.*, p.name as product_name, s.store_name
    FROM rfq_requests r
    JOIN products p ON r.product_id = p.id
    JOIN stores s ON r.store_id = s.id
    WHERE r.retailer_id = ?
    ORDER BY r.created_at DESC
    LIMIT 5
");
$stmt->execute([$user_id]);
$recent_rfqs = $stmt->fetchAll();

// Featured products
$stmt = $pdo->query("
    SELECT p.*, s.store_name, s.city,
           (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
    FROM products p
    JOIN stores s ON p.store_id = s.id
    WHERE p.is_featured = 1 AND p.status = 'active' AND s.status = 'active'
    ORDER BY RAND()
    LIMIT 6
");
$featured_products = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retailer Dashboard - Tradexa</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="browse.php" class="text-gray-700 hover:text-blue-600">Browse Products</a>
                    <a href="cart.php" class="text-gray-700 hover:text-blue-600">
                        <i class="fas fa-shopping-cart"></i> Cart
                    </a>
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="browse.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-search mr-3"></i> Browse Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> My Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="wishlist.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-heart mr-3"></i> Wishlist
                        </a>
                    </li>
                    <li>
                        <a href="suppliers.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Followed Suppliers
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Dashboard Overview</h2>
                <a href="browse.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    <i class="fas fa-search mr-1"></i> Browse Products
                </a>
            </div>
            
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-shopping-cart text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Orders</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_orders']); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-clock text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Pending Orders</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['pending_orders']); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100 text-red-600">
                            <i class="fas fa-heart text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Wishlist Items</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['wishlist_items']); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-quote-left text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">RFQ Requests</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['rfq_requests']); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Recent Orders -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent Orders</h3>
                    </div>
                    <div class="p-6">
                        <?php if (empty($recent_orders)): ?>
                            <p class="text-gray-500">No orders yet</p>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($recent_orders as $order): ?>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium"><?php echo $order['order_number']; ?></p>
                                            <p class="text-sm text-gray-600"><?php echo $order['item_count']; ?> items</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-medium"><?php echo format_currency($order['final_amount']); ?></p>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <?php echo ucfirst($order['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent RFQs -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent RFQ Requests</h3>
                    </div>
                    <div class="p-6">
                        <?php if (empty($recent_rfqs)): ?>
                            <p class="text-gray-500">No RFQ requests yet</p>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($recent_rfqs as $rfq): ?>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium"><?php echo $rfq['product_name']; ?></p>
                                            <p class="text-sm text-gray-600"><?php echo $rfq['store_name']; ?></p>
                                            <p class="text-sm text-gray-600">Qty: <?php echo $rfq['quantity']; ?></p>
                                        </div>
                                        <div class="text-right">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                <?php echo $rfq['status'] === 'quoted' ? 'bg-green-100 text-green-800' : 
                                                    ($rfq['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'); ?>">
                                                <?php echo ucfirst($rfq['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Featured Products -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Featured Products</h3>
                </div>
                <div class="p-6">
                    <?php if (empty($featured_products)): ?>
                        <p class="text-gray-500">No featured products available</p>
                    <?php else: ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php foreach ($featured_products as $product): ?>
                                <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow">
                                    <?php if ($product['primary_image']): ?>
                                        <img src="../<?php echo $product['primary_image']; ?>" alt="<?php echo $product['name']; ?>" class="w-full h-32 object-cover rounded mb-3">
                                    <?php else: ?>
                                        <div class="w-full h-32 bg-gray-200 rounded mb-3 flex items-center justify-center">
                                            <i class="fas fa-image text-gray-400 text-2xl"></i>
                                        </div>
                                    <?php endif; ?>
                                    <h4 class="font-medium text-gray-900 mb-1"><?php echo $product['name']; ?></h4>
                                    <p class="text-sm text-gray-600 mb-2"><?php echo $product['store_name']; ?>, <?php echo $product['city']; ?></p>
                                    <p class="text-lg font-bold text-blue-600"><?php echo format_currency($product['base_price']); ?></p>
                                    <p class="text-xs text-gray-500">Min Order: <?php echo $product['min_order_quantity']; ?> <?php echo $product['unit']; ?></p>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
