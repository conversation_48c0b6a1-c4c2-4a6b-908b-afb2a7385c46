<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin role
require_role('admin');

$current_user = get_logged_in_user();

$error = '';
$success = '';

// Handle settings updates
if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_general') {
            $site_name = sanitize_input($_POST['site_name']);
            $site_description = sanitize_input($_POST['site_description']);
            $contact_email = sanitize_input($_POST['contact_email']);
            $contact_phone = sanitize_input($_POST['contact_phone']);
            
            // Update settings (you would typically store these in a settings table)
            $success = 'General settings updated successfully!';
            
        } elseif ($action === 'update_email') {
            $smtp_host = sanitize_input($_POST['smtp_host']);
            $smtp_port = sanitize_input($_POST['smtp_port']);
            $smtp_username = sanitize_input($_POST['smtp_username']);
            $smtp_password = $_POST['smtp_password'];
            
            // Update email settings
            $success = 'Email settings updated successfully!';
            
        } elseif ($action === 'update_payment') {
            $payment_gateway = $_POST['payment_gateway'];
            $razorpay_key = sanitize_input($_POST['razorpay_key']);
            $razorpay_secret = sanitize_input($_POST['razorpay_secret']);
            
            // Update payment settings
            $success = 'Payment settings updated successfully!';
        }
    }
}

// Get current settings (mock data - you would fetch from database)
$settings = [
    'site_name' => 'Tradexa',
    'site_description' => 'Wholesale E-Commerce Platform',
    'contact_email' => '<EMAIL>',
    'contact_phone' => '+91 9876543210',
    'smtp_host' => 'smtp.gmail.com',
    'smtp_port' => '587',
    'smtp_username' => '<EMAIL>',
    'payment_gateway' => 'razorpay',
    'razorpay_key' => '',
    'razorpay_secret' => ''
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Tradexa Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Admin</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="users.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-users mr-3"></i> Users
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="categories.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tags mr-3"></i> Categories
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="settings.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-cog mr-3"></i> Settings
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Platform Settings</h2>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <div class="space-y-6">
                <!-- General Settings -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">General Settings</h3>
                    </div>
                    <div class="p-6">
                        <form method="POST" class="space-y-4">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="update_general">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Site Name</label>
                                    <input type="text" name="site_name" value="<?php echo htmlspecialchars($settings['site_name']); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                                    <input type="email" name="contact_email" value="<?php echo htmlspecialchars($settings['contact_email']); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
                                    <input type="tel" name="contact_phone" value="<?php echo htmlspecialchars($settings['contact_phone']); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Site Description</label>
                                <textarea name="site_description" rows="3" 
                                          class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"><?php echo htmlspecialchars($settings['site_description']); ?></textarea>
                            </div>
                            
                            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-save mr-1"></i> Save General Settings
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Email Settings -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Email Settings</h3>
                    </div>
                    <div class="p-6">
                        <form method="POST" class="space-y-4">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="update_email">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">SMTP Host</label>
                                    <input type="text" name="smtp_host" value="<?php echo htmlspecialchars($settings['smtp_host']); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">SMTP Port</label>
                                    <input type="number" name="smtp_port" value="<?php echo htmlspecialchars($settings['smtp_port']); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">SMTP Username</label>
                                    <input type="text" name="smtp_username" value="<?php echo htmlspecialchars($settings['smtp_username']); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">SMTP Password</label>
                                    <input type="password" name="smtp_password" placeholder="Enter new password to change" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            
                            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-save mr-1"></i> Save Email Settings
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Payment Settings -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Payment Settings</h3>
                    </div>
                    <div class="p-6">
                        <form method="POST" class="space-y-4">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="update_payment">
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Payment Gateway</label>
                                <select name="payment_gateway" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="razorpay" <?php echo $settings['payment_gateway'] === 'razorpay' ? 'selected' : ''; ?>>Razorpay</option>
                                    <option value="stripe" <?php echo $settings['payment_gateway'] === 'stripe' ? 'selected' : ''; ?>>Stripe</option>
                                    <option value="paypal" <?php echo $settings['payment_gateway'] === 'paypal' ? 'selected' : ''; ?>>PayPal</option>
                                </select>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Razorpay Key ID</label>
                                    <input type="text" name="razorpay_key" value="<?php echo htmlspecialchars($settings['razorpay_key']); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Razorpay Secret</label>
                                    <input type="password" name="razorpay_secret" placeholder="Enter secret to change" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            
                            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-save mr-1"></i> Save Payment Settings
                            </button>
                        </form>
                    </div>
                </div>

                <!-- System Information -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">System Information</h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm font-medium text-gray-700">PHP Version</p>
                                <p class="text-sm text-gray-900"><?php echo phpversion(); ?></p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700">Server Software</p>
                                <p class="text-sm text-gray-900"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700">Database</p>
                                <p class="text-sm text-gray-900">MySQL <?php echo $pdo->query('SELECT VERSION()')->fetchColumn(); ?></p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700">Platform Version</p>
                                <p class="text-sm text-gray-900">Tradexa v1.0.0</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
