<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user has temp session from registration
if (!isset($_SESSION['temp_user_id'])) {
    header('Location: login.php');
    exit();
}

$user_id = $_SESSION['temp_user_id'];
$error = '';
$success = '';

// Get user details
$stmt = $pdo->prepare("SELECT email, first_name, last_name FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: login.php');
    exit();
}

if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token';
    } else {
        $otp = sanitize_input($_POST['otp']);
        
        if (empty($otp)) {
            $error = 'Please enter the OTP';
        } elseif (verify_otp($user_id, $otp)) {
            // Update user status to active
            $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE id = ?");
            $stmt->execute([$user_id]);
            
            // Clear temp session
            unset($_SESSION['temp_user_id']);
            
            // Track email verification
            track_event('email_verified', ['user_id' => $user_id], $user_id);
            
            $success = 'Email verified successfully! You can now login to your account.';
        } else {
            $error = 'Invalid or expired OTP';
        }
    }
}

// Handle resend OTP
if (isset($_GET['resend'])) {
    $otp = generate_otp();
    $user_name = $user['first_name'] . ' ' . $user['last_name'];
    if (save_otp($user_id, $otp) && send_otp_email($user['email'], $otp, $user_name)) {
        // Store new OTP in session for development mode
        if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
            $_SESSION['temp_otp'] = $otp;
        }
        $success = 'OTP resent successfully!';
    } else {
        $error = 'Failed to resend OTP. Please try again.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Email - Tradexa</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <div class="text-center">
                    <h1 class="text-3xl font-bold text-blue-600">Tradexa</h1>
                    <p class="text-gray-600">Wholesale Marketplace</p>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    Verify Your Email
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    We've sent a 6-digit OTP to <strong><?php echo $user['email']; ?></strong>
                </p>

                <?php if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE && isset($_SESSION['temp_otp'])): ?>
                    <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                            <div>
                                <p class="text-sm font-medium text-yellow-800">Development Mode</p>
                                <p class="text-sm text-yellow-700">Your OTP code is: <strong class="text-lg"><?php echo $_SESSION['temp_otp']; ?></strong></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    <?php echo $success; ?>
                    <?php if (strpos($success, 'verified successfully') !== false): ?>
                        <div class="mt-3">
                            <a href="login.php" class="text-blue-600 hover:text-blue-500 font-medium">
                                Click here to login
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!$success || strpos($success, 'verified successfully') === false): ?>
                <form class="mt-8 space-y-6" method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    
                    <div>
                        <label for="otp" class="block text-sm font-medium text-gray-700">Enter OTP</label>
                        <input id="otp" name="otp" type="text" maxlength="6" required 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-center text-2xl tracking-widest" 
                               placeholder="000000" autocomplete="off">
                        <p class="mt-1 text-xs text-gray-500">Enter the 6-digit code sent to your email</p>
                    </div>

                    <div>
                        <button type="submit" 
                                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <i class="fas fa-check text-blue-500 group-hover:text-blue-400"></i>
                            </span>
                            Verify Email
                        </button>
                    </div>
                </form>
                
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Didn't receive the code? 
                        <a href="?resend=1" class="font-medium text-blue-600 hover:text-blue-500">
                            Resend OTP
                        </a>
                    </p>
                    <p class="mt-2 text-xs text-gray-500">
                        The OTP will expire in 10 minutes
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // Auto-focus on OTP input
        document.getElementById('otp').focus();
        
        // Auto-submit when 6 digits are entered
        document.getElementById('otp').addEventListener('input', function(e) {
            if (e.target.value.length === 6) {
                // Small delay to allow user to see the complete OTP
                setTimeout(() => {
                    e.target.form.submit();
                }, 500);
            }
        });
    </script>
</body>
</html>
