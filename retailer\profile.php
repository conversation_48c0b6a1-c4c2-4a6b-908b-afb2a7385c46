<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require retailer role
require_role('retailer');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

$error = '';
$success = '';

// Get user profile
$stmt = $pdo->prepare("SELECT * FROM user_profiles WHERE user_id = ?");
$stmt->execute([$user_id]);
$profile = $stmt->fetch();

// Handle form submission
if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_profile') {
            $first_name = sanitize_input($_POST['first_name']);
            $last_name = sanitize_input($_POST['last_name']);
            $phone = sanitize_input($_POST['phone']);
            $business_name = sanitize_input($_POST['business_name']);
            $business_type = sanitize_input($_POST['business_type']);
            $gst_number = sanitize_input($_POST['gst_number']);
            $address_line1 = sanitize_input($_POST['address_line1']);
            $address_line2 = sanitize_input($_POST['address_line2']);
            $city = sanitize_input($_POST['city']);
            $state = sanitize_input($_POST['state']);
            $pincode = sanitize_input($_POST['pincode']);
            
            try {
                $pdo->beginTransaction();
                
                // Update users table
                $stmt = $pdo->prepare("UPDATE users SET first_name = ?, last_name = ?, phone = ? WHERE id = ?");
                $stmt->execute([$first_name, $last_name, $phone, $user_id]);
                
                // Update or insert profile
                if ($profile) {
                    $stmt = $pdo->prepare("
                        UPDATE user_profiles SET 
                        business_name = ?, business_type = ?, gst_number = ?,
                        address_line1 = ?, address_line2 = ?, city = ?, state = ?, pincode = ?
                        WHERE user_id = ?
                    ");
                    $stmt->execute([
                        $business_name, $business_type, $gst_number,
                        $address_line1, $address_line2, $city, $state, $pincode, $user_id
                    ]);
                } else {
                    $stmt = $pdo->prepare("
                        INSERT INTO user_profiles 
                        (user_id, business_name, business_type, gst_number,
                         address_line1, address_line2, city, state, pincode)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $user_id, $business_name, $business_type, $gst_number,
                        $address_line1, $address_line2, $city, $state, $pincode
                    ]);
                }
                
                $pdo->commit();
                $success = 'Profile updated successfully!';
                
                // Refresh user data
                $current_user = get_logged_in_user();
                $stmt = $pdo->prepare("SELECT * FROM user_profiles WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $profile = $stmt->fetch();
                
            } catch (Exception $e) {
                $pdo->rollback();
                $error = 'Failed to update profile. Please try again.';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Tradexa</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="browse.php" class="text-gray-700 hover:text-blue-600">Browse Products</a>
                    <a href="cart.php" class="text-gray-700 hover:text-blue-600">
                        <i class="fas fa-shopping-cart"></i> Cart
                    </a>
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="browse.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-search mr-3"></i> Browse Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> My Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="wishlist.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-heart mr-3"></i> Wishlist
                        </a>
                    </li>
                    <li>
                        <a href="suppliers.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Followed Suppliers
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Profile Settings</h2>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Personal & Business Information</h3>
                </div>
                <div class="p-6">
                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="update_profile">
                        
                        <!-- Personal Information -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">Personal Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">First Name *</label>
                                    <input type="text" name="first_name" value="<?php echo htmlspecialchars($current_user['first_name']); ?>" required 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
                                    <input type="text" name="last_name" value="<?php echo htmlspecialchars($current_user['last_name']); ?>" required 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input type="email" value="<?php echo htmlspecialchars($current_user['email']); ?>" disabled 
                                           class="w-full p-2 border border-gray-300 rounded-lg bg-gray-100">
                                    <p class="text-xs text-gray-500 mt-1">Email cannot be changed</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                                    <input type="tel" name="phone" value="<?php echo htmlspecialchars($current_user['phone'] ?? ''); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Business Information -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">Business Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
                                    <input type="text" name="business_name" value="<?php echo htmlspecialchars($profile['business_name'] ?? ''); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Business Type</label>
                                    <select name="business_type" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">Select Business Type</option>
                                        <option value="retailer" <?php echo ($profile['business_type'] ?? '') === 'retailer' ? 'selected' : ''; ?>>Retailer</option>
                                        <option value="distributor" <?php echo ($profile['business_type'] ?? '') === 'distributor' ? 'selected' : ''; ?>>Distributor</option>
                                        <option value="reseller" <?php echo ($profile['business_type'] ?? '') === 'reseller' ? 'selected' : ''; ?>>Reseller</option>
                                        <option value="online_seller" <?php echo ($profile['business_type'] ?? '') === 'online_seller' ? 'selected' : ''; ?>>Online Seller</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">GST Number</label>
                                <input type="text" name="gst_number" value="<?php echo htmlspecialchars($profile['gst_number'] ?? ''); ?>" 
                                       class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        
                        <!-- Address Information -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">Address Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                                    <input type="text" name="address_line1" value="<?php echo htmlspecialchars($profile['address_line1'] ?? ''); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Address Line 2</label>
                                    <input type="text" name="address_line2" value="<?php echo htmlspecialchars($profile['address_line2'] ?? ''); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                    <input type="text" name="city" value="<?php echo htmlspecialchars($profile['city'] ?? ''); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">State</label>
                                    <input type="text" name="state" value="<?php echo htmlspecialchars($profile['state'] ?? ''); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Pincode</label>
                                    <input type="text" name="pincode" value="<?php echo htmlspecialchars($profile['pincode'] ?? ''); ?>" 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-save mr-1"></i> Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
