<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Redirect if already logged in
if (is_logged_in()) {
    header('Location: ../index.php');
    exit();
}

$error = '';
$success = '';
$user_type = $_GET['type'] ?? 'retailer';

if (!in_array($user_type, ['retailer', 'wholesaler'])) {
    $user_type = 'retailer';
}

if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token';
    } else {
        $email = sanitize_input($_POST['email']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];
        $first_name = sanitize_input($_POST['first_name']);
        $last_name = sanitize_input($_POST['last_name']);
        $phone = sanitize_input($_POST['phone']);
        $business_name = sanitize_input($_POST['business_name'] ?? '');
        $role = $_POST['role'];
        
        // Validation
        if (empty($email) || empty($password) || empty($first_name) || empty($last_name)) {
            $error = 'Please fill in all required fields';
        } elseif ($password !== $confirm_password) {
            $error = 'Passwords do not match';
        } elseif (strlen($password) < 6) {
            $error = 'Password must be at least 6 characters long';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Invalid email address';
        } elseif (!in_array($role, ['retailer', 'wholesaler'])) {
            $error = 'Invalid user type';
        } else {
            // Check if email already exists
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                $error = 'Email address already registered';
            } else {
                try {
                    $pdo->beginTransaction();
                    
                    // Create user
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("INSERT INTO users (email, password, role, first_name, last_name, phone, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$email, $hashed_password, $role, $first_name, $last_name, $phone, $_SERVER['REMOTE_ADDR']]);
                    $user_id = $pdo->lastInsertId();
                    
                    // Create user profile
                    $stmt = $pdo->prepare("INSERT INTO user_profiles (user_id, business_name) VALUES (?, ?)");
                    $stmt->execute([$user_id, $business_name]);
                    
                    // Generate and send OTP
                    $otp = generate_otp();
                    save_otp($user_id, $otp);
                    
                    if (send_otp_email($email, $otp)) {
                        $pdo->commit();
                        
                        // Track registration event
                        track_event('user_registration', ['user_id' => $user_id, 'role' => $role], $user_id);
                        
                        $_SESSION['temp_user_id'] = $user_id;
                        header('Location: verify-email.php');
                        exit();
                    } else {
                        $pdo->rollback();
                        $error = 'Failed to send verification email. Please try again.';
                    }
                } catch (Exception $e) {
                    $pdo->rollback();
                    $error = 'Registration failed. Please try again.';
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Tradexa</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <div class="text-center">
                    <h1 class="text-3xl font-bold text-blue-600">Tradexa</h1>
                    <p class="text-gray-600">Wholesale Marketplace</p>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    Create your account
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Join as a <?php echo ucfirst($user_type); ?>
                </p>
            </div>
            
            <!-- User Type Selector -->
            <div class="flex rounded-lg bg-gray-100 p-1">
                <a href="?type=retailer" class="flex-1 text-center py-2 px-4 rounded-md text-sm font-medium <?php echo $user_type === 'retailer' ? 'bg-white text-blue-600 shadow' : 'text-gray-600'; ?>">
                    <i class="fas fa-shopping-cart mr-1"></i> Retailer
                </a>
                <a href="?type=wholesaler" class="flex-1 text-center py-2 px-4 rounded-md text-sm font-medium <?php echo $user_type === 'wholesaler' ? 'bg-white text-blue-600 shadow' : 'text-gray-600'; ?>">
                    <i class="fas fa-store mr-1"></i> Wholesaler
                </a>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <form class="mt-8 space-y-6" method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="role" value="<?php echo $user_type; ?>">
                
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700">First Name *</label>
                            <input id="first_name" name="first_name" type="text" required 
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" 
                                   value="<?php echo $_POST['first_name'] ?? ''; ?>">
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name *</label>
                            <input id="last_name" name="last_name" type="text" required 
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" 
                                   value="<?php echo $_POST['last_name'] ?? ''; ?>">
                        </div>
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address *</label>
                        <input id="email" name="email" type="email" required 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" 
                               value="<?php echo $_POST['email'] ?? ''; ?>">
                    </div>
                    
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input id="phone" name="phone" type="tel" 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" 
                               value="<?php echo $_POST['phone'] ?? ''; ?>">
                    </div>
                    
                    <div>
                        <label for="business_name" class="block text-sm font-medium text-gray-700">
                            Business Name <?php echo $user_type === 'wholesaler' ? '*' : '(Optional)'; ?>
                        </label>
                        <input id="business_name" name="business_name" type="text" <?php echo $user_type === 'wholesaler' ? 'required' : ''; ?>
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" 
                               value="<?php echo $_POST['business_name'] ?? ''; ?>">
                    </div>
                    
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">Password *</label>
                        <input id="password" name="password" type="password" required 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <p class="mt-1 text-xs text-gray-500">Minimum 6 characters</p>
                    </div>
                    
                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm Password *</label>
                        <input id="confirm_password" name="confirm_password" type="password" required 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <div>
                    <button type="submit" 
                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-user-plus text-blue-500 group-hover:text-blue-400"></i>
                        </span>
                        Create Account
                    </button>
                </div>
                
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Already have an account? 
                        <a href="login.php" class="font-medium text-blue-600 hover:text-blue-500">
                            Sign in here
                        </a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
