<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require wholesaler role
require_role('wholesaler');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

// Get orders for this wholesaler's stores
$stmt = $pdo->prepare("
    SELECT o.*, oi.*, p.name as product_name, p.sku, s.store_name,
           u.first_name, u.last_name, u.email as customer_email
    FROM order_items oi
    JOIN orders o ON oi.order_id = o.id
    JOIN products p ON oi.product_id = p.id
    JOIN stores s ON oi.store_id = s.id
    JOIN users u ON o.retailer_id = u.id
    WHERE s.wholesaler_id = ?
    ORDER BY o.created_at DESC
");
$stmt->execute([$user_id]);
$order_items = $stmt->fetchAll();

// Group order items by order
$orders = [];
foreach ($order_items as $item) {
    $order_id = $item['order_id'];
    if (!isset($orders[$order_id])) {
        $orders[$order_id] = [
            'order_info' => $item,
            'items' => []
        ];
    }
    $orders[$order_id]['items'][] = $item;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orders - Tradexa Wholesaler</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Wholesaler</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> My Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Orders</h2>
                <div class="flex space-x-2">
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>All Orders</option>
                        <option>Pending</option>
                        <option>Confirmed</option>
                        <option>Processing</option>
                        <option>Shipped</option>
                        <option>Delivered</option>
                    </select>
                </div>
            </div>

            <?php if (empty($orders)): ?>
                <!-- No Orders -->
                <div class="bg-white rounded-lg shadow p-8 text-center">
                    <i class="fas fa-shopping-cart text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Orders Yet</h3>
                    <p class="text-gray-600 mb-4">You haven't received any orders yet. Make sure your products are active and visible.</p>
                    <a href="products.php" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                        Manage Products
                    </a>
                </div>
            <?php else: ?>
                <!-- Orders List -->
                <div class="space-y-6">
                    <?php foreach ($orders as $order_id => $order_data): ?>
                        <?php $order = $order_data['order_info']; ?>
                        <div class="bg-white rounded-lg shadow">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">
                                            Order #<?php echo $order['order_number']; ?>
                                        </h3>
                                        <p class="text-sm text-gray-600">
                                            Customer: <?php echo $order['first_name'] . ' ' . $order['last_name']; ?>
                                            (<?php echo $order['customer_email']; ?>)
                                        </p>
                                        <p class="text-sm text-gray-600">
                                            Order Date: <?php echo date('M d, Y H:i', strtotime($order['created_at'])); ?>
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                                            <?php 
                                            switch($order['status']) {
                                                case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                                case 'confirmed': echo 'bg-blue-100 text-blue-800'; break;
                                                case 'processing': echo 'bg-purple-100 text-purple-800'; break;
                                                case 'shipped': echo 'bg-indigo-100 text-indigo-800'; break;
                                                case 'delivered': echo 'bg-green-100 text-green-800'; break;
                                                case 'cancelled': echo 'bg-red-100 text-red-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800';
                                            }
                                            ?>">
                                            <?php echo ucfirst($order['status']); ?>
                                        </span>
                                        <p class="text-lg font-bold text-gray-900 mt-1">
                                            <?php echo format_currency($order['final_amount']); ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="p-6">
                                <h4 class="font-medium text-gray-900 mb-3">Order Items</h4>
                                <div class="space-y-3">
                                    <?php foreach ($order_data['items'] as $item): ?>
                                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                            <div class="flex-1">
                                                <h5 class="font-medium text-gray-900"><?php echo htmlspecialchars($item['product_name']); ?></h5>
                                                <p class="text-sm text-gray-600">
                                                    Store: <?php echo htmlspecialchars($item['store_name']); ?> | 
                                                    SKU: <?php echo htmlspecialchars($item['sku']); ?>
                                                </p>
                                                <p class="text-sm text-gray-600">
                                                    Quantity: <?php echo $item['quantity']; ?> × <?php echo format_currency($item['unit_price']); ?>
                                                </p>
                                            </div>
                                            <div class="text-right">
                                                <p class="font-semibold text-gray-900">
                                                    <?php echo format_currency($item['total_price']); ?>
                                                </p>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                                    <?php 
                                                    switch($item['status']) {
                                                        case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                                        case 'confirmed': echo 'bg-blue-100 text-blue-800'; break;
                                                        case 'processing': echo 'bg-purple-100 text-purple-800'; break;
                                                        case 'shipped': echo 'bg-indigo-100 text-indigo-800'; break;
                                                        case 'delivered': echo 'bg-green-100 text-green-800'; break;
                                                        case 'cancelled': echo 'bg-red-100 text-red-800'; break;
                                                        default: echo 'bg-gray-100 text-gray-800';
                                                    }
                                                    ?>">
                                                    <?php echo ucfirst($item['status']); ?>
                                                </span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <div class="mt-4 flex justify-between items-center">
                                    <div class="flex space-x-2">
                                        <?php if ($order['status'] === 'pending'): ?>
                                            <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm">
                                                <i class="fas fa-check mr-1"></i> Confirm Order
                                            </button>
                                            <button class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 text-sm">
                                                <i class="fas fa-times mr-1"></i> Reject
                                            </button>
                                        <?php elseif ($order['status'] === 'confirmed'): ?>
                                            <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm">
                                                <i class="fas fa-cog mr-1"></i> Start Processing
                                            </button>
                                        <?php elseif ($order['status'] === 'processing'): ?>
                                            <button class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 text-sm">
                                                <i class="fas fa-shipping-fast mr-1"></i> Mark as Shipped
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <button class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 text-sm">
                                            <i class="fas fa-eye mr-1"></i> View Details
                                        </button>
                                        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm">
                                            <i class="fas fa-print mr-1"></i> Print Invoice
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
