<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require wholesaler role
require_role('wholesaler');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

$error = '';
$success = '';

// Get user's stores
$stmt = $pdo->prepare("SELECT * FROM stores WHERE wholesaler_id = ? AND status = 'active' ORDER BY store_name");
$stmt->execute([$user_id]);
$stores = $stmt->fetchAll();

// Get categories
$stmt = $pdo->query("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
$categories = $stmt->fetchAll();

// Handle form submission
if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token';
    } else {
        $store_id = $_POST['store_id'] ?? '';
        $name = sanitize_input($_POST['name']);
        $sku = sanitize_input($_POST['sku']);
        $category_id = $_POST['category_id'] ?: null;
        $description = sanitize_input($_POST['description']);
        $base_price = $_POST['base_price'] ?? 0;
        $min_order_quantity = $_POST['min_order_quantity'] ?? 1;
        $stock_quantity = $_POST['stock_quantity'] ?? 0;
        $unit = sanitize_input($_POST['unit']);
        $is_featured = isset($_POST['is_featured']) ? 1 : 0;
        
        if (empty($store_id) || empty($name) || empty($sku) || empty($base_price)) {
            $error = 'Please fill in all required fields';
        } else {
            // Check if SKU already exists
            $stmt = $pdo->prepare("SELECT id FROM products WHERE sku = ?");
            $stmt->execute([$sku]);
            if ($stmt->fetch()) {
                $error = 'SKU already exists. Please use a unique SKU.';
            } else {
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO products (store_id, name, sku, category_id, description, base_price, 
                                            min_order_quantity, stock_quantity, unit, is_featured, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
                    ");
                    
                    if ($stmt->execute([$store_id, $name, $sku, $category_id, $description, $base_price, 
                                      $min_order_quantity, $stock_quantity, $unit, $is_featured])) {
                        $product_id = $pdo->lastInsertId();
                        $success = 'Product added successfully!';
                        
                        // Clear form data
                        $_POST = [];
                    } else {
                        $error = 'Failed to add product. Please try again.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Product - Tradexa Wholesaler</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Wholesaler</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> My Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Add New Product</h2>
                <a href="products.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Products
                </a>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <?php if (empty($stores)): ?>
                <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
                    You need to have at least one active store to add products. 
                    <a href="stores.php" class="underline">Create a store first</a>.
                </div>
            <?php else: ?>
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Product Information</h3>
                    </div>
                    <div class="p-6">
                        <form method="POST" class="space-y-6">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            
                            <!-- Store Selection -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Store *</label>
                                <select name="store_id" required class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Select Store</option>
                                    <?php foreach ($stores as $store): ?>
                                        <option value="<?php echo $store['id']; ?>" <?php echo ($_POST['store_id'] ?? '') == $store['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($store['store_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <!-- Basic Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Product Name *</label>
                                    <input type="text" name="name" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">SKU *</label>
                                    <input type="text" name="sku" value="<?php echo htmlspecialchars($_POST['sku'] ?? ''); ?>" required 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <p class="text-xs text-gray-500 mt-1">Unique product identifier</p>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                                <select name="category_id" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" <?php echo ($_POST['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                <textarea name="description" rows="4" 
                                          class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                            </div>
                            
                            <!-- Pricing and Inventory -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Base Price (INR) *</label>
                                    <input type="number" name="base_price" step="0.01" min="0" value="<?php echo htmlspecialchars($_POST['base_price'] ?? ''); ?>" required 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Min Order Quantity *</label>
                                    <input type="number" name="min_order_quantity" min="1" value="<?php echo htmlspecialchars($_POST['min_order_quantity'] ?? '1'); ?>" required 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Stock Quantity *</label>
                                    <input type="number" name="stock_quantity" min="0" value="<?php echo htmlspecialchars($_POST['stock_quantity'] ?? ''); ?>" required 
                                           class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Unit *</label>
                                <select name="unit" required class="w-full p-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Select Unit</option>
                                    <option value="piece" <?php echo ($_POST['unit'] ?? '') === 'piece' ? 'selected' : ''; ?>>Piece</option>
                                    <option value="kg" <?php echo ($_POST['unit'] ?? '') === 'kg' ? 'selected' : ''; ?>>Kilogram</option>
                                    <option value="gram" <?php echo ($_POST['unit'] ?? '') === 'gram' ? 'selected' : ''; ?>>Gram</option>
                                    <option value="liter" <?php echo ($_POST['unit'] ?? '') === 'liter' ? 'selected' : ''; ?>>Liter</option>
                                    <option value="meter" <?php echo ($_POST['unit'] ?? '') === 'meter' ? 'selected' : ''; ?>>Meter</option>
                                    <option value="box" <?php echo ($_POST['unit'] ?? '') === 'box' ? 'selected' : ''; ?>>Box</option>
                                    <option value="pack" <?php echo ($_POST['unit'] ?? '') === 'pack' ? 'selected' : ''; ?>>Pack</option>
                                </select>
                            </div>
                            
                            <!-- Options -->
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" name="is_featured" value="1" <?php echo isset($_POST['is_featured']) ? 'checked' : ''; ?> 
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Feature this product</span>
                                </label>
                            </div>
                            
                            <div class="flex justify-end space-x-4">
                                <a href="products.php" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700">
                                    Cancel
                                </a>
                                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                    <i class="fas fa-save mr-1"></i> Add Product
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
