<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Require login
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit();
}

$user_id = $_SESSION['user_id'];
$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'add':
                $product_id = $input['product_id'] ?? 0;
                $quantity = $input['quantity'] ?? 1;
                
                if (!$product_id || !$quantity) {
                    throw new Exception('Product ID and quantity are required');
                }
                
                // Get or create cart
                $stmt = $pdo->prepare("SELECT id FROM carts WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $cart = $stmt->fetch();
                
                if (!$cart) {
                    $stmt = $pdo->prepare("INSERT INTO carts (user_id) VALUES (?)");
                    $stmt->execute([$user_id]);
                    $cart_id = $pdo->lastInsertId();
                } else {
                    $cart_id = $cart['id'];
                }
                
                // Get product details
                $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
                $stmt->execute([$product_id]);
                $product = $stmt->fetch();
                
                if (!$product) {
                    throw new Exception('Product not found');
                }
                
                // Check if item already in cart
                $stmt = $pdo->prepare("SELECT * FROM cart_items WHERE cart_id = ? AND product_id = ?");
                $stmt->execute([$cart_id, $product_id]);
                $existing_item = $stmt->fetch();
                
                if ($existing_item) {
                    // Update quantity
                    $new_quantity = $existing_item['quantity'] + $quantity;
                    $stmt = $pdo->prepare("UPDATE cart_items SET quantity = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$new_quantity, $existing_item['id']]);
                } else {
                    // Add new item
                    $stmt = $pdo->prepare("INSERT INTO cart_items (cart_id, product_id, quantity, unit_price) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$cart_id, $product_id, $quantity, $product['base_price']]);
                }
                
                echo json_encode(['success' => true, 'message' => 'Item added to cart']);
                break;
                
            case 'remove':
                $product_id = $input['product_id'] ?? 0;
                
                if (!$product_id) {
                    throw new Exception('Product ID is required');
                }
                
                $stmt = $pdo->prepare("
                    DELETE ci FROM cart_items ci 
                    JOIN carts c ON ci.cart_id = c.id 
                    WHERE c.user_id = ? AND ci.product_id = ?
                ");
                $stmt->execute([$user_id, $product_id]);
                
                echo json_encode(['success' => true, 'message' => 'Item removed from cart']);
                break;
                
            case 'update':
                $product_id = $input['product_id'] ?? 0;
                $quantity = $input['quantity'] ?? 1;
                
                if (!$product_id || !$quantity) {
                    throw new Exception('Product ID and quantity are required');
                }
                
                $stmt = $pdo->prepare("
                    UPDATE cart_items ci 
                    JOIN carts c ON ci.cart_id = c.id 
                    SET ci.quantity = ?, ci.updated_at = NOW()
                    WHERE c.user_id = ? AND ci.product_id = ?
                ");
                $stmt->execute([$quantity, $user_id, $product_id]);
                
                echo json_encode(['success' => true, 'message' => 'Cart updated']);
                break;
                
            case 'clear':
                $stmt = $pdo->prepare("
                    DELETE ci FROM cart_items ci 
                    JOIN carts c ON ci.cart_id = c.id 
                    WHERE c.user_id = ?
                ");
                $stmt->execute([$user_id]);
                
                echo json_encode(['success' => true, 'message' => 'Cart cleared']);
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } elseif ($method === 'GET') {
        // Get cart items
        $stmt = $pdo->prepare("
            SELECT ci.*, p.name, p.sku, p.base_price, p.unit, p.min_order_quantity,
                   s.store_name,
                   (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as image
            FROM cart_items ci
            JOIN carts c ON ci.cart_id = c.id
            JOIN products p ON ci.product_id = p.id
            JOIN stores s ON p.store_id = s.id
            WHERE c.user_id = ?
            ORDER BY ci.created_at DESC
        ");
        $stmt->execute([$user_id]);
        $items = $stmt->fetchAll();
        
        $total = 0;
        foreach ($items as &$item) {
            $item['total_price'] = $item['quantity'] * $item['unit_price'];
            $total += $item['total_price'];
        }
        
        echo json_encode([
            'success' => true,
            'items' => $items,
            'total' => $total,
            'count' => count($items)
        ]);
        
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
