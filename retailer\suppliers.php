<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require retailer role
require_role('retailer');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

// Get followed suppliers
$stmt = $pdo->prepare("
    SELECT sf.*, s.store_name, s.description, s.city, s.state, s.business_type,
           u.first_name, u.last_name, u.email,
           (SELECT COUNT(*) FROM products WHERE store_id = s.id AND status = 'active') as product_count
    FROM store_followers sf
    JOIN stores s ON sf.store_id = s.id
    JOIN users u ON s.wholesaler_id = u.id
    WHERE sf.user_id = ? AND s.status = 'active'
    ORDER BY sf.followed_at DESC
");
$stmt->execute([$user_id]);
$followed_suppliers = $stmt->fetchAll();

// Get recommended suppliers (not followed)
$stmt = $pdo->prepare("
    SELECT s.*, u.first_name, u.last_name,
           (SELECT COUNT(*) FROM products WHERE store_id = s.id AND status = 'active') as product_count
    FROM stores s
    JOIN users u ON s.wholesaler_id = u.id
    WHERE s.status = 'active'
    AND s.id NOT IN (SELECT store_id FROM store_followers WHERE user_id = ?)
    ORDER BY product_count DESC
    LIMIT 10
");
$stmt->execute([$user_id]);
$recommended_suppliers = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suppliers - Tradexa</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="browse.php" class="text-gray-700 hover:text-blue-600">Browse Products</a>
                    <a href="cart.php" class="text-gray-700 hover:text-blue-600">
                        <i class="fas fa-shopping-cart"></i> Cart
                    </a>
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="browse.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-search mr-3"></i> Browse Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> My Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="wishlist.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-heart mr-3"></i> Wishlist
                        </a>
                    </li>
                    <li>
                        <a href="suppliers.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-store mr-3"></i> Followed Suppliers
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Suppliers</h2>
            </div>

            <!-- Followed Suppliers -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Followed Suppliers (<?php echo count($followed_suppliers); ?>)</h3>
                
                <?php if (empty($followed_suppliers)): ?>
                    <div class="bg-white rounded-lg shadow p-6 text-center">
                        <i class="fas fa-store text-3xl text-gray-400 mb-3"></i>
                        <p class="text-gray-500">You're not following any suppliers yet.</p>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($followed_suppliers as $supplier): ?>
                            <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
                                <div class="flex justify-between items-start mb-4">
                                    <div>
                                        <h4 class="font-bold text-lg text-gray-900"><?php echo htmlspecialchars($supplier['store_name']); ?></h4>
                                        <p class="text-sm text-gray-600"><?php echo htmlspecialchars($supplier['first_name'] . ' ' . $supplier['last_name']); ?></p>
                                    </div>
                                    <button class="text-red-500 hover:text-red-700 unfollow-btn" data-store-id="<?php echo $supplier['store_id']; ?>">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                </div>
                                
                                <div class="space-y-2 mb-4">
                                    <p class="text-sm text-gray-600">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        <?php echo htmlspecialchars($supplier['city']); ?><?php echo $supplier['state'] ? ', ' . htmlspecialchars($supplier['state']) : ''; ?>
                                    </p>
                                    <?php if ($supplier['business_type']): ?>
                                        <p class="text-sm text-gray-600">
                                            <i class="fas fa-building mr-1"></i>
                                            <?php echo ucfirst($supplier['business_type']); ?>
                                        </p>
                                    <?php endif; ?>
                                    <p class="text-sm text-gray-600">
                                        <i class="fas fa-box mr-1"></i>
                                        <?php echo number_format($supplier['product_count']); ?> Products
                                    </p>
                                    <p class="text-sm text-gray-600">
                                        <i class="fas fa-calendar mr-1"></i>
                                        Following since <?php echo date('M Y', strtotime($supplier['followed_at'])); ?>
                                    </p>
                                </div>
                                
                                <?php if ($supplier['description']): ?>
                                    <p class="text-sm text-gray-600 mb-4"><?php echo htmlspecialchars(substr($supplier['description'], 0, 100)); ?><?php echo strlen($supplier['description']) > 100 ? '...' : ''; ?></p>
                                <?php endif; ?>
                                
                                <div class="flex space-x-2">
                                    <a href="supplier-view.php?id=<?php echo $supplier['store_id']; ?>" class="flex-1 text-center bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700">
                                        <i class="fas fa-eye mr-1"></i> View Store
                                    </a>
                                    <a href="browse.php?store=<?php echo $supplier['store_id']; ?>" class="flex-1 text-center bg-green-600 text-white py-2 px-3 rounded text-sm hover:bg-green-700">
                                        <i class="fas fa-shopping-cart mr-1"></i> Shop
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recommended Suppliers -->
            <?php if (!empty($recommended_suppliers)): ?>
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Recommended Suppliers</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($recommended_suppliers as $supplier): ?>
                            <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
                                <div class="flex justify-between items-start mb-4">
                                    <div>
                                        <h4 class="font-bold text-lg text-gray-900"><?php echo htmlspecialchars($supplier['store_name']); ?></h4>
                                        <p class="text-sm text-gray-600"><?php echo htmlspecialchars($supplier['first_name'] . ' ' . $supplier['last_name']); ?></p>
                                    </div>
                                    <button class="text-gray-400 hover:text-red-500 follow-btn" data-store-id="<?php echo $supplier['id']; ?>">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                                
                                <div class="space-y-2 mb-4">
                                    <p class="text-sm text-gray-600">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        <?php echo htmlspecialchars($supplier['city']); ?><?php echo $supplier['state'] ? ', ' . htmlspecialchars($supplier['state']) : ''; ?>
                                    </p>
                                    <?php if ($supplier['business_type']): ?>
                                        <p class="text-sm text-gray-600">
                                            <i class="fas fa-building mr-1"></i>
                                            <?php echo ucfirst($supplier['business_type']); ?>
                                        </p>
                                    <?php endif; ?>
                                    <p class="text-sm text-gray-600">
                                        <i class="fas fa-box mr-1"></i>
                                        <?php echo number_format($supplier['product_count']); ?> Products
                                    </p>
                                </div>
                                
                                <?php if ($supplier['description']): ?>
                                    <p class="text-sm text-gray-600 mb-4"><?php echo htmlspecialchars(substr($supplier['description'], 0, 100)); ?><?php echo strlen($supplier['description']) > 100 ? '...' : ''; ?></p>
                                <?php endif; ?>
                                
                                <div class="flex space-x-2">
                                    <a href="supplier-view.php?id=<?php echo $supplier['id']; ?>" class="flex-1 text-center bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700">
                                        <i class="fas fa-eye mr-1"></i> View Store
                                    </a>
                                    <a href="browse.php?store=<?php echo $supplier['id']; ?>" class="flex-1 text-center bg-green-600 text-white py-2 px-3 rounded text-sm hover:bg-green-700">
                                        <i class="fas fa-shopping-cart mr-1"></i> Shop
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Follow/Unfollow functionality
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('follow-btn') || e.target.parentElement.classList.contains('follow-btn')) {
                e.preventDefault();
                const button = e.target.classList.contains('follow-btn') ? e.target : e.target.parentElement;
                const storeId = button.dataset.storeId;
                
                // AJAX call to follow supplier
                fetch('/api/suppliers.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'follow',
                        store_id: storeId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload(); // Refresh to update the lists
                    } else {
                        alert('Failed to follow supplier');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error following supplier');
                });
            }
            
            if (e.target.classList.contains('unfollow-btn') || e.target.parentElement.classList.contains('unfollow-btn')) {
                e.preventDefault();
                const button = e.target.classList.contains('unfollow-btn') ? e.target : e.target.parentElement;
                const storeId = button.dataset.storeId;
                
                if (confirm('Unfollow this supplier?')) {
                    // AJAX call to unfollow supplier
                    fetch('/api/suppliers.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'unfollow',
                            store_id: storeId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload(); // Refresh to update the lists
                        } else {
                            alert('Failed to unfollow supplier');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error unfollowing supplier');
                    });
                }
            }
        });
    </script>
</body>
</html>
