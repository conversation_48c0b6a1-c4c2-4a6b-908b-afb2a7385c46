<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require wholesaler role
require_role('wholesaler');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

// Get wholesaler statistics
$stats = [];

// Total stores
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM stores WHERE wholesaler_id = ?");
$stmt->execute([$user_id]);
$stats['total_stores'] = $stmt->fetch()['total'];

// Total products
$stmt = $pdo->prepare("
    SELECT COUNT(*) as total 
    FROM products p 
    JOIN stores s ON p.store_id = s.id 
    WHERE s.wholesaler_id = ?
");
$stmt->execute([$user_id]);
$stats['total_products'] = $stmt->fetch()['total'];

// Total orders
$stmt = $pdo->prepare("
    SELECT COUNT(*) as total 
    FROM order_items oi 
    JOIN stores s ON oi.store_id = s.id 
    WHERE s.wholesaler_id = ?
");
$stmt->execute([$user_id]);
$stats['total_orders'] = $stmt->fetch()['total'];

// Pending RFQs
$stmt = $pdo->prepare("
    SELECT COUNT(*) as total 
    FROM rfq_requests r 
    JOIN stores s ON r.store_id = s.id 
    WHERE s.wholesaler_id = ? AND r.status = 'pending'
");
$stmt->execute([$user_id]);
$stats['pending_rfqs'] = $stmt->fetch()['total'];

// Recent orders
$stmt = $pdo->prepare("
    SELECT o.*, oi.*, p.name as product_name, u.first_name, u.last_name
    FROM order_items oi
    JOIN orders o ON oi.order_id = o.id
    JOIN products p ON oi.product_id = p.id
    JOIN users u ON o.retailer_id = u.id
    JOIN stores s ON oi.store_id = s.id
    WHERE s.wholesaler_id = ?
    ORDER BY o.created_at DESC
    LIMIT 5
");
$stmt->execute([$user_id]);
$recent_orders = $stmt->fetchAll();

// Get stores
$stmt = $pdo->prepare("SELECT * FROM stores WHERE wholesaler_id = ? ORDER BY created_at DESC");
$stmt->execute([$user_id]);
$stores = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wholesaler Dashboard - Tradexa</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Wholesaler</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> My Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">Dashboard Overview</h2>
                <a href="stores.php?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    <i class="fas fa-plus mr-1"></i> Add New Store
                </a>
            </div>
            
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-store text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">My Stores</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_stores']); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-box text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Products</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_products']); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-shopping-cart text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Orders</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_orders']); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-quote-left text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Pending RFQs</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['pending_rfqs']); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Orders -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent Orders</h3>
                    </div>
                    <div class="p-6">
                        <?php if (empty($recent_orders)): ?>
                            <p class="text-gray-500">No orders yet</p>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($recent_orders as $order): ?>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium"><?php echo $order['order_number']; ?></p>
                                            <p class="text-sm text-gray-600"><?php echo $order['product_name']; ?></p>
                                            <p class="text-sm text-gray-600"><?php echo $order['first_name'] . ' ' . $order['last_name']; ?></p>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-medium"><?php echo format_currency($order['total_price']); ?></p>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <?php echo ucfirst($order['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- My Stores -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">My Stores</h3>
                    </div>
                    <div class="p-6">
                        <?php if (empty($stores)): ?>
                            <div class="text-center py-8">
                                <i class="fas fa-store text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-500 mb-4">You haven't created any stores yet</p>
                                <a href="stores.php?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                    Create Your First Store
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($stores as $store): ?>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium"><?php echo $store['store_name']; ?></p>
                                            <p class="text-sm text-gray-600"><?php echo $store['city']; ?></p>
                                        </div>
                                        <div class="text-right">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                <?php echo $store['status'] === 'active' ? 'bg-green-100 text-green-800' : 
                                                    ($store['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'); ?>">
                                                <?php echo ucfirst($store['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
