<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require wholesaler role
require_role('wholesaler');

$current_user = get_logged_in_user();
$user_id = $current_user['id'];

// Get RFQ requests for this wholesaler's stores
$stmt = $pdo->prepare("
    SELECT r.*, p.name as product_name, p.sku, p.base_price, s.store_name,
           u.first_name, u.last_name, u.email as customer_email,
           q.quoted_price, q.min_quantity as quote_min_qty, q.delivery_time, q.valid_until
    FROM rfq_requests r
    JOIN products p ON r.product_id = p.id
    JOIN stores s ON r.store_id = s.id
    JOIN users u ON r.retailer_id = u.id
    LEFT JOIN rfq_quotes q ON r.id = q.rfq_id
    WHERE s.wholesaler_id = ?
    ORDER BY r.created_at DESC
");
$stmt->execute([$user_id]);
$rfq_requests = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RFQ Requests - Tradexa Wholesaler</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">Tradexa Wholesaler</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo $current_user['first_name']; ?></span>
                    <a href="../auth/logout.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="stores.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-store mr-3"></i> My Stores
                        </a>
                    </li>
                    <li>
                        <a href="products.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-box mr-3"></i> Products
                        </a>
                    </li>
                    <li>
                        <a href="orders.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-shopping-cart mr-3"></i> Orders
                        </a>
                    </li>
                    <li>
                        <a href="rfq.php" class="flex items-center p-2 text-blue-600 bg-blue-50 rounded-lg">
                            <i class="fas fa-quote-left mr-3"></i> RFQ Requests
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i> Analytics
                        </a>
                    </li>
                    <li>
                        <a href="profile.php" class="flex items-center p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-user mr-3"></i> Profile
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-900">RFQ Requests</h2>
                <div class="flex space-x-2">
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>All Requests</option>
                        <option>Pending</option>
                        <option>Quoted</option>
                        <option>Accepted</option>
                        <option>Rejected</option>
                    </select>
                </div>
            </div>

            <?php if (empty($rfq_requests)): ?>
                <!-- No RFQ Requests -->
                <div class="bg-white rounded-lg shadow p-8 text-center">
                    <i class="fas fa-quote-left text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No RFQ Requests</h3>
                    <p class="text-gray-600 mb-4">You haven't received any quote requests yet. Promote your products to get more inquiries.</p>
                    <a href="products.php" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                        Manage Products
                    </a>
                </div>
            <?php else: ?>
                <!-- RFQ Requests List -->
                <div class="space-y-6">
                    <?php foreach ($rfq_requests as $rfq): ?>
                        <div class="bg-white rounded-lg shadow">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">
                                            RFQ #<?php echo $rfq['id']; ?>
                                        </h3>
                                        <p class="text-sm text-gray-600">
                                            Customer: <?php echo $rfq['first_name'] . ' ' . $rfq['last_name']; ?>
                                            (<?php echo $rfq['customer_email']; ?>)
                                        </p>
                                        <p class="text-sm text-gray-600">
                                            Request Date: <?php echo date('M d, Y H:i', strtotime($rfq['created_at'])); ?>
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                                            <?php 
                                            switch($rfq['status']) {
                                                case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                                case 'quoted': echo 'bg-blue-100 text-blue-800'; break;
                                                case 'accepted': echo 'bg-green-100 text-green-800'; break;
                                                case 'rejected': echo 'bg-red-100 text-red-800'; break;
                                                case 'expired': echo 'bg-gray-100 text-gray-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800';
                                            }
                                            ?>">
                                            <?php echo ucfirst($rfq['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Product Details -->
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">Product Details</h4>
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <h5 class="font-medium text-gray-900"><?php echo htmlspecialchars($rfq['product_name']); ?></h5>
                                            <p class="text-sm text-gray-600">Store: <?php echo htmlspecialchars($rfq['store_name']); ?></p>
                                            <p class="text-sm text-gray-600">SKU: <?php echo htmlspecialchars($rfq['sku']); ?></p>
                                            <p class="text-sm text-gray-600">Base Price: <?php echo format_currency($rfq['base_price']); ?></p>
                                            <p class="text-sm text-gray-600">Requested Quantity: <strong><?php echo $rfq['quantity']; ?></strong></p>
                                            <?php if ($rfq['message']): ?>
                                                <div class="mt-2">
                                                    <p class="text-sm font-medium text-gray-700">Customer Message:</p>
                                                    <p class="text-sm text-gray-600 italic">"<?php echo htmlspecialchars($rfq['message']); ?>"</p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <!-- Quote Details -->
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">Quote Details</h4>
                                        <?php if ($rfq['quoted_price']): ?>
                                            <div class="bg-blue-50 p-4 rounded-lg">
                                                <p class="text-sm text-gray-600">Quoted Price: <strong class="text-blue-600"><?php echo format_currency($rfq['quoted_price']); ?></strong></p>
                                                <p class="text-sm text-gray-600">Minimum Quantity: <?php echo $rfq['quote_min_qty']; ?></p>
                                                <?php if ($rfq['delivery_time']): ?>
                                                    <p class="text-sm text-gray-600">Delivery Time: <?php echo htmlspecialchars($rfq['delivery_time']); ?></p>
                                                <?php endif; ?>
                                                <?php if ($rfq['valid_until']): ?>
                                                    <p class="text-sm text-gray-600">Valid Until: <?php echo date('M d, Y', strtotime($rfq['valid_until'])); ?></p>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="bg-yellow-50 p-4 rounded-lg">
                                                <p class="text-sm text-yellow-800">No quote provided yet</p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="mt-6 flex justify-between items-center">
                                    <div class="flex space-x-2">
                                        <?php if ($rfq['status'] === 'pending'): ?>
                                            <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm">
                                                <i class="fas fa-quote-right mr-1"></i> Provide Quote
                                            </button>
                                            <button class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 text-sm">
                                                <i class="fas fa-times mr-1"></i> Decline
                                            </button>
                                        <?php elseif ($rfq['status'] === 'quoted'): ?>
                                            <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm">
                                                <i class="fas fa-edit mr-1"></i> Update Quote
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <button class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 text-sm">
                                            <i class="fas fa-comment mr-1"></i> Message Customer
                                        </button>
                                        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm">
                                            <i class="fas fa-eye mr-1"></i> View Details
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
